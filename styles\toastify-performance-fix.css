/**
 * Performance-Optimized Toastify Animations
 * 
 * This file overrides the default react-toastify animations to use
 * transform-only animations that don't trigger layout recalculations.
 * 
 * Original issue: 'visibility' property in keyframes triggers layout
 * Solution: Use opacity and transform for GPU-accelerated animations
 */

/* Override the default slideInRight animation */
@keyframes Toastify__slideInRight {
  0% {
    transform: translate3d(110%, 0, 0) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

/* Override the default slideOutRight animation */
@keyframes Toastify__slideOutRight {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate3d(110%, 0, 0) scale(0.95);
    opacity: 0;
  }
}

/* Override the default slideInLeft animation */
@keyframes Toastify__slideInLeft {
  0% {
    transform: translate3d(-110%, 0, 0) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

/* Override the default slideOutLeft animation */
@keyframes Toastify__slideOutLeft {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate3d(-110%, 0, 0) scale(0.95);
    opacity: 0;
  }
}

/* Override the default slideInUp animation */
@keyframes Toastify__slideInUp {
  0% {
    transform: translate3d(0, 110%, 0) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

/* Override the default slideOutUp animation */
@keyframes Toastify__slideOutUp {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -110%, 0) scale(0.95);
    opacity: 0;
  }
}

/* Override the default slideInDown animation */
@keyframes Toastify__slideInDown {
  0% {
    transform: translate3d(0, -110%, 0) scale(0.95);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

/* Override the default slideOutDown animation */
@keyframes Toastify__slideOutDown {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 110%, 0) scale(0.95);
    opacity: 0;
  }
}

/* Override bounce animations for better performance */
@keyframes Toastify__bounceInRight {
  0% {
    transform: translate3d(100%, 0, 0) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translate3d(-5%, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

@keyframes Toastify__bounceOutRight {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  20% {
    transform: translate3d(-5%, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(100%, 0, 0) scale(0.8);
    opacity: 0;
  }
}

@keyframes Toastify__bounceInLeft {
  0% {
    transform: translate3d(-100%, 0, 0) scale(0.8);
    opacity: 0;
  }
  60% {
    transform: translate3d(5%, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
}

@keyframes Toastify__bounceOutLeft {
  0% {
    transform: translate3d(0, 0, 0) scale(1);
    opacity: 1;
  }
  20% {
    transform: translate3d(5%, 0, 0) scale(1.05);
    opacity: 1;
  }
  100% {
    transform: translate3d(-100%, 0, 0) scale(0.8);
    opacity: 0;
  }
}

/* Override zoom animations */
@keyframes Toastify__zoomIn {
  0% {
    transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
  }
  50% {
    transform: scale3d(1.05, 1.05, 1.05);
    opacity: 1;
  }
  100% {
    transform: scale3d(1, 1, 1);
    opacity: 1;
  }
}

@keyframes Toastify__zoomOut {
  0% {
    transform: scale3d(1, 1, 1);
    opacity: 1;
  }
  50% {
    transform: scale3d(1.05, 1.05, 1.05);
    opacity: 1;
  }
  100% {
    transform: scale3d(0.3, 0.3, 0.3);
    opacity: 0;
  }
}

/* Override flip animations */
@keyframes Toastify__flipIn {
  0% {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
  40% {
    transform: perspective(400px) rotateX(-10deg);
    opacity: 1;
  }
  70% {
    transform: perspective(400px) rotateX(10deg);
    opacity: 1;
  }
  100% {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
}

@keyframes Toastify__flipOut {
  0% {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
  30% {
    transform: perspective(400px) rotateX(-10deg);
    opacity: 1;
  }
  100% {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
}

/* Ensure all toast containers use hardware acceleration */
.Toastify__toast-container {
  will-change: transform;
  transform: translateZ(0);
}

.Toastify__toast {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Performance optimization for toast progress bar */
.Toastify__progress-bar {
  will-change: transform;
  transform: translateZ(0);
}

/* Ensure smooth animations on all devices */
@media (prefers-reduced-motion: no-preference) {
  .Toastify__toast {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .Toastify__toast {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .Toastify__toast-container * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
