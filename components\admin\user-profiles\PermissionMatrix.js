import { useState, useEffect } from 'react'
import styles from '@/styles/admin/user-profiles/PermissionMatrix.module.css'
import * as userProfiles from '@/lib/user-profiles'

export default function PermissionMatrix({ onError, onLoading }) {
  const [permissions, setPermissions] = useState({})
  const [roles] = useState(['dev', 'admin', 'artist', 'braider', 'user'])
  const [categories] = useState(['access', 'users', 'finance', 'system', 'marketing', 'analytics', 'inventory', 'bookings', 'customers', 'sales', 'content'])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [changes, setChanges] = useState({})

  // Default permission matrix
  const defaultPermissions = {
    admin_panel_access: {
      name: 'Admin Panel Access',
      category: 'access',
      description: 'Access to admin panel',
      dev: true, admin: true, artist: true, braider: true, user: false
    },
    user_management: {
      name: 'User Management',
      category: 'users',
      description: 'Manage users and roles',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    user_profiles_management: {
      name: 'User Profiles Management',
      category: 'users',
      description: 'Manage user profiles and permissions',
      dev: true, admin: false, artist: false, braider: false, user: false
    },
    commission_management: {
      name: 'Commission Management',
      category: 'finance',
      description: 'Manage commission rates',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    commission_view: {
      name: 'Commission View',
      category: 'finance',
      description: 'View own commission rates',
      dev: true, admin: true, artist: true, braider: true, user: false
    },
    system_settings: {
      name: 'System Settings',
      category: 'system',
      description: 'Access system settings',
      dev: true, admin: false, artist: false, braider: false, user: false
    },
    diagnostics_access: {
      name: 'Diagnostics Access',
      category: 'system',
      description: 'Access diagnostic tools',
      dev: true, admin: false, artist: false, braider: false, user: false
    },
    marketing_access: {
      name: 'Marketing Access',
      category: 'marketing',
      description: 'Access marketing tools',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    analytics_access: {
      name: 'Analytics Access',
      category: 'analytics',
      description: 'Access analytics',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    inventory_management: {
      name: 'Inventory Management',
      category: 'inventory',
      description: 'Manage inventory',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    booking_management: {
      name: 'Booking Management',
      category: 'bookings',
      description: 'Manage bookings',
      dev: true, admin: true, artist: true, braider: true, user: false
    },
    customer_management: {
      name: 'Customer Management',
      category: 'customers',
      description: 'Manage customers',
      dev: true, admin: true, artist: true, braider: true, user: false
    },
    pos_access: {
      name: 'POS Access',
      category: 'sales',
      description: 'Access POS terminal',
      dev: true, admin: true, artist: true, braider: true, user: false
    },
    payment_management: {
      name: 'Payment Management',
      category: 'finance',
      description: 'Manage payments',
      dev: true, admin: true, artist: false, braider: false, user: false
    },
    gallery_management: {
      name: 'Gallery Management',
      category: 'content',
      description: 'Manage gallery',
      dev: true, admin: true, artist: false, braider: false, user: false
    }
  }

  useEffect(() => {
    loadPermissions()
  }, [])

  const loadPermissions = async () => {
    try {
      setLoading(true)
      onLoading?.(true)

      console.log('PermissionMatrix: Loading permissions from API...')

      // Fetch permissions from the API
      const response = await userProfiles.fetchPermissions()

      if (response.success && response.permissions) {
        setPermissions(response.permissions)
        console.log('PermissionMatrix: Permissions loaded successfully from API')
      } else {
        throw new Error('Invalid response format')
      }

    } catch (error) {
      console.error('PermissionMatrix: Error loading permissions:', error)
      console.warn('PermissionMatrix: Falling back to default permissions')

      // Fall back to default permissions if API fails
      setPermissions(defaultPermissions)
      onError?.('Failed to load permissions from database, using defaults')
    } finally {
      setLoading(false)
      onLoading?.(false)
    }
  }

  const handlePermissionChange = (permissionKey, role, value) => {
    setPermissions(prev => ({
      ...prev,
      [permissionKey]: {
        ...prev[permissionKey],
        [role]: value
      }
    }))

    setChanges(prev => ({
      ...prev,
      [`${permissionKey}_${role}`]: value
    }))
  }

  const saveChanges = async () => {
    try {
      setSaving(true)

      console.log('PermissionMatrix: Saving permission changes to API...', changes)

      // Save permissions to the API
      const response = await userProfiles.savePermissions(permissions)

      if (response.success) {
        setChanges({})
        console.log('PermissionMatrix: Permissions saved successfully to database')

        // Optionally reload permissions to ensure consistency
        await loadPermissions()
      } else {
        throw new Error(response.message || 'Failed to save permissions')
      }

    } catch (error) {
      console.error('PermissionMatrix: Error saving permissions:', error)
      onError?.(`Failed to save permission changes: ${error.message}`)
    } finally {
      setSaving(false)
    }
  }

  const resetChanges = async () => {
    try {
      console.log('PermissionMatrix: Resetting changes...')

      // Reload permissions from the API to reset to database state
      await loadPermissions()
      setChanges({})

      console.log('PermissionMatrix: Changes reset successfully')
    } catch (error) {
      console.error('PermissionMatrix: Error resetting changes:', error)

      // Fall back to default permissions if API fails
      setPermissions(defaultPermissions)
      setChanges({})
    }
  }

  const getCategoryColor = (category) => {
    const colors = {
      access: '#4ecdc4',
      users: '#45b7d1',
      finance: '#96ceb4',
      system: '#ff6b6b',
      marketing: '#feca57',
      analytics: '#a777e3',
      inventory: '#fd79a8',
      bookings: '#6c5ce7',
      customers: '#00b894',
      sales: '#e17055',
      content: '#74b9ff'
    }
    return colors[category] || '#95a5a6'
  }

  const groupedPermissions = categories.reduce((acc, category) => {
    acc[category] = Object.entries(permissions).filter(([key, permission]) =>
      permission.category === category
    )
    return acc
  }, {})

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading permission matrix...</p>
      </div>
    )
  }

  return (
    <div className={styles.permissionMatrix}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h2>Permission Matrix</h2>
          <p>Configure role-based access control for admin panel features</p>
        </div>
        <div className={styles.headerActions}>
          {Object.keys(changes).length > 0 && (
            <>
              <button
                className={styles.resetButton}
                onClick={resetChanges}
                disabled={saving}
              >
                Reset Changes
              </button>
              <button
                className={styles.saveButton}
                onClick={saveChanges}
                disabled={saving}
              >
                {saving ? 'Saving...' : `Save Changes (${Object.keys(changes).length})`}
              </button>
            </>
          )}
        </div>
      </div>

      <div className={styles.matrixContainer}>
        {/* Role Headers */}
        <div className={styles.roleHeaders}>
          <div className={styles.permissionHeader}>Permission</div>
          {roles.map(role => (
            <div key={role} className={styles.roleHeader}>
              <span className={styles.roleIcon}>
                {role === 'dev' && '⚙️'}
                {role === 'admin' && '👑'}
                {role === 'artist' && '🎨'}
                {role === 'braider' && '💇‍♀️'}
                {role === 'user' && '👤'}
              </span>
              <span className={styles.roleName}>
                {role.charAt(0).toUpperCase() + role.slice(1)}
              </span>
            </div>
          ))}
        </div>

        {/* Permission Categories */}
        {categories.map(category => {
          const categoryPermissions = groupedPermissions[category]
          if (categoryPermissions.length === 0) return null

          return (
            <div key={category} className={styles.categorySection}>
              <div
                className={styles.categoryHeader}
                style={{ borderLeftColor: getCategoryColor(category) }}
              >
                <h3>{category.charAt(0).toUpperCase() + category.slice(1)}</h3>
              </div>

              {categoryPermissions.map(([permissionKey, permission]) => (
                <div key={permissionKey} className={styles.permissionRow}>
                  <div className={styles.permissionInfo}>
                    <h4>{permission.name}</h4>
                    <p>{permission.description}</p>
                  </div>

                  {roles.map(role => (
                    <div key={role} className={styles.permissionCell}>
                      <label className={styles.checkboxLabel}>
                        <input
                          type="checkbox"
                          checked={permission[role] || false}
                          onChange={(e) => handlePermissionChange(permissionKey, role, e.target.checked)}
                          disabled={role === 'dev' || saving} // DEV always has all permissions
                        />
                        <span className={styles.checkboxCustom}></span>
                      </label>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )
        })}
      </div>

      {/* Legend */}
      <div className={styles.legend}>
        <h3>Legend</h3>
        <div className={styles.legendItems}>
          <div className={styles.legendItem}>
            <span className={styles.legendIcon}>⚙️</span>
            <span>DEV: Unrestricted access (cannot be modified)</span>
          </div>
          <div className={styles.legendItem}>
            <span className={styles.legendIcon}>👑</span>
            <span>Admin: Full business management access</span>
          </div>
          <div className={styles.legendItem}>
            <span className={styles.legendIcon}>🎨</span>
            <span>Artist: Service-focused access with booking management</span>
          </div>
          <div className={styles.legendItem}>
            <span className={styles.legendIcon}>💇‍♀️</span>
            <span>Braider: Service-focused access with booking management</span>
          </div>
          <div className={styles.legendItem}>
            <span className={styles.legendIcon}>👤</span>
            <span>User: Basic access level</span>
          </div>
        </div>
      </div>
    </div>
  )
}
