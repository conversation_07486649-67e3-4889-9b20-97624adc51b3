.settingsPage {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
}

.header {
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 0.75rem;
  flex-shrink: 0;
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.errorBox {
  background-color: #fed7d7;
  border: 1px solid #f56565;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.errorBox p {
  color: #c53030;
  margin: 0;
}

.errorBox button {
  background-color: #fff;
  border: 1px solid #c53030;
  color: #c53030;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.errorBox button:hover {
  background-color: #c53030;
  color: #fff;
}

.successBox {
  background-color: #c6f6d5;
  border: 1px solid #48bb78;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  flex-shrink: 0;
}

.successBox p {
  color: #2f855a;
  margin: 0;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  font-size: 1rem;
  color: #4a5568;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header h1 {
    font-size: 1.3rem;
  }

  .errorBox,
  .successBox {
    padding: 0.5rem;
    margin-bottom: 0.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .errorBox button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .header h1 {
    font-size: 1.2rem;
  }

  .errorBox,
  .successBox {
    padding: 0.4rem;
  }
}
