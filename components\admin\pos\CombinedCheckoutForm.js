import { useState } from 'react';
import CustomerInfoForm from './CustomerInfoForm';
import PaymentMethodSelector from './PaymentMethodSelector';
import POSSquarePayment from './POSSquarePayment';
import styles from '@/styles/admin/POS.module.css';

/**
 * CombinedCheckoutForm component for handling customer info and payment in one viewport
 * 
 * @param {Object} props - Component props
 * @param {number} props.amount - Total amount to charge
 * @param {Function} props.onCustomerData - Callback when customer data is provided
 * @param {Function} props.onPaymentMethodSelect - Callback when payment method is selected
 * @param {Function} props.onSquarePaymentSuccess - Callback for successful Square payment
 * @param {Function} props.onSquarePaymentError - Callback for Square payment error
 * @param {boolean} props.isProcessing - Whether payment is being processed
 * @param {Object} props.customerData - Current customer data
 * @param {string} props.paymentMethod - Selected payment method
 * @param {Object} props.orderDetails - Order details for payment
 * @returns {JSX.Element}
 */
export default function CombinedCheckoutForm({
  amount,
  onCustomerData,
  onPaymentMethodSelect,
  onSquarePaymentSuccess,
  onSquarePaymentError,
  isProcessing,
  customerData,
  paymentMethod,
  orderDetails
}) {
  const [formStep, setFormStep] = useState('customer'); // 'customer', 'payment', 'processing'

  const handleCustomerSubmit = (data) => {
    onCustomerData(data);
    setFormStep('payment');
  };

  const handlePaymentMethodSelect = (method) => {
    onPaymentMethodSelect(method);
    if (method === 'cash') {
      setFormStep('processing');
    }
  };

  return (
    <div className={styles.combinedCheckoutForm}>
      <div className={styles.checkoutGrid}>
        {/* Customer Information Section */}
        <div className={styles.customerSection}>
          <div className={styles.sectionHeader}>
            <h3>Customer Information</h3>
            {customerData && (
              <button 
                className={styles.editButton}
                onClick={() => setFormStep('customer')}
                disabled={isProcessing}
              >
                Edit
              </button>
            )}
          </div>
          
          {formStep === 'customer' || !customerData ? (
            <CustomerInfoForm
              onCustomerData={handleCustomerSubmit}
              isLoading={isProcessing}
              initialData={customerData}
            />
          ) : (
            <div className={styles.customerSummary}>
              <p><strong>Name:</strong> {customerData.name}</p>
              {customerData.email && <p><strong>Email:</strong> {customerData.email}</p>}
              {customerData.phone && <p><strong>Phone:</strong> {customerData.phone}</p>}
            </div>
          )}
        </div>

        {/* Payment Section */}
        <div className={styles.paymentSection}>
          <div className={styles.sectionHeader}>
            <h3>Payment Method</h3>
            {paymentMethod && formStep !== 'customer' && (
              <button 
                className={styles.editButton}
                onClick={() => setFormStep('payment')}
                disabled={isProcessing}
              >
                Change
              </button>
            )}
          </div>

          {formStep === 'customer' && !customerData ? (
            <div className={styles.paymentDisabled}>
              <p>Please complete customer information first</p>
            </div>
          ) : formStep === 'payment' || !paymentMethod ? (
            <PaymentMethodSelector
              onPaymentMethodSelect={handlePaymentMethodSelect}
              amount={amount}
              isLoading={isProcessing}
            />
          ) : paymentMethod === 'cash' && formStep === 'processing' ? (
            <div className={styles.processingCash}>
              <div className={styles.processingIcon}>💵</div>
              <h4>Processing Cash Payment</h4>
              <p>Recording transaction...</p>
              <div className={styles.loadingSpinner}></div>
            </div>
          ) : paymentMethod === 'card' ? (
            <div className={styles.cardPaymentSection}>
              <div className={styles.paymentMethodSummary}>
                <p><strong>Payment Method:</strong> Credit/Debit Card</p>
                <p><strong>Amount:</strong> ${amount.toFixed(2)} AUD</p>
              </div>
              <POSSquarePayment
                amount={amount}
                currency="AUD"
                onSuccess={onSquarePaymentSuccess}
                onError={onSquarePaymentError}
                orderDetails={orderDetails}
              />
            </div>
          ) : null}
        </div>
      </div>

      {/* Progress Indicator */}
      <div className={styles.checkoutProgress}>
        <div className={`${styles.progressStep} ${customerData ? styles.completed : styles.active}`}>
          <span className={styles.stepNumber}>1</span>
          <span className={styles.stepLabel}>Customer Info</span>
        </div>
        <div className={styles.progressLine}></div>
        <div className={`${styles.progressStep} ${paymentMethod ? styles.completed : customerData ? styles.active : ''}`}>
          <span className={styles.stepNumber}>2</span>
          <span className={styles.stepLabel}>Payment</span>
        </div>
      </div>
    </div>
  );
}
