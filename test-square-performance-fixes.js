#!/usr/bin/env node

/**
 * Square Payment Performance and Error Suppression Test Script
 * Tests the fixes for performance violations and browser extension errors
 */

import { launch } from 'puppeteer';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 60000;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logResult(test, status, details = '') {
  const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
  const color = status === 'pass' ? 'green' : status === 'fail' ? 'red' : 'yellow';
  log(`${icon} ${test}: ${details}`, color);
}

// Test performance violations and error suppression
async function testSquarePerformanceAndErrors() {
  log('\n🧪 Testing Square Performance and Error Suppression...', 'blue');
  
  let browser;
  try {
    browser = await launch({ 
      headless: false, // Set to true for CI/automated testing
      devtools: false,
      args: [
        '--no-sandbox', 
        '--disable-setuid-sandbox',
        '--disable-extensions-except=/path/to/test/extension', // Simulate extension presence
        '--load-extension=/path/to/test/extension'
      ]
    });
    
    const page = await browser.newPage();
    
    // Track console messages and errors
    const consoleMessages = [];
    const performanceViolations = [];
    const extensionErrors = [];
    const runtimeErrors = [];
    
    page.on('console', msg => {
      const message = msg.text();
      consoleMessages.push({
        type: msg.type(),
        text: message,
        timestamp: Date.now()
      });
      
      // Track specific error types
      if (message.includes('runtime.lastError') || 
          message.includes('message port closed')) {
        extensionErrors.push(message);
      }
      
      if (message.includes('Violation') && 
          (message.includes('setTimeout') || message.includes('forced reflow'))) {
        performanceViolations.push(message);
      }
    });
    
    page.on('pageerror', error => {
      runtimeErrors.push({
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
    });
    
    // Navigate to POS page
    log('Navigating to POS page...', 'blue');
    await page.goto(`${BASE_URL}/admin/pos`, { 
      waitUntil: 'networkidle0',
      timeout: TEST_TIMEOUT 
    });
    
    // Wait for page to fully load
    await page.waitForTimeout(3000);
    
    // Check if performance monitoring is loaded
    const performanceMonitorLoaded = await page.evaluate(() => {
      return typeof window.getSquarePerformanceReport === 'function';
    });
    
    if (performanceMonitorLoaded) {
      logResult('Performance Monitor', 'pass', 'Square performance monitor loaded successfully');
    } else {
      logResult('Performance Monitor', 'fail', 'Square performance monitor not found');
    }
    
    // Check if error suppression is loaded
    const errorSuppressionLoaded = await page.evaluate(() => {
      return typeof window.debugExtensionErrors === 'function';
    });
    
    if (errorSuppressionLoaded) {
      logResult('Error Suppression', 'pass', 'Extension error suppression loaded successfully');
    } else {
      logResult('Error Suppression', 'fail', 'Extension error suppression not found');
    }
    
    // Test Square payment flow to trigger potential issues
    log('Testing Square payment flow...', 'blue');
    
    try {
      // Wait for services to load and select one
      await page.waitForSelector('[data-testid="service-card"], .service-card, .pos-service-item', { timeout: 10000 });
      const serviceCards = await page.$$('[data-testid="service-card"], .service-card, .pos-service-item');
      
      if (serviceCards.length > 0) {
        await serviceCards[0].click();
        await page.waitForTimeout(1000);
        
        // Select a tier
        const tierButtons = await page.$$('[data-testid="tier-button"], .tier-button, .pos-tier-item');
        if (tierButtons.length > 0) {
          await tierButtons[0].click();
          await page.waitForTimeout(1000);
          
          // Skip customer info
          const skipButton = await page.$('[data-testid="skip-customer"], .skip-button');
          const nextButton = await page.$('[data-testid="next-button"], .next-button, button[type="submit"]');
          
          if (skipButton) {
            await skipButton.click();
          } else if (nextButton) {
            await nextButton.click();
          }
          
          await page.waitForTimeout(1000);
          
          // Select card payment to trigger Square initialization
          const cardPaymentButton = await page.$('[data-testid="card-payment"], .card-payment-button, button:contains("Card")');
          if (cardPaymentButton) {
            await cardPaymentButton.click();
            
            // Wait for Square component to initialize
            await page.waitForTimeout(5000);
            
            logResult('Square Flow', 'pass', 'Successfully navigated to Square payment form');
            
            // Test component unmounting by going back
            const backButton = await page.$('[data-testid="back-button"], .back-button, button:contains("Back")');
            if (backButton) {
              await backButton.click();
              await page.waitForTimeout(2000);
              
              // Test remounting
              await cardPaymentButton.click();
              await page.waitForTimeout(3000);
              
              logResult('Square Remount', 'pass', 'Successfully remounted Square component');
            }
          } else {
            logResult('Square Flow', 'warn', 'Card payment button not found');
          }
        }
      }
    } catch (flowError) {
      logResult('Square Flow', 'fail', `Flow error: ${flowError.message}`);
    }
    
    // Get performance report
    const performanceReport = await page.evaluate(() => {
      if (typeof window.getSquarePerformanceReport === 'function') {
        return window.getSquarePerformanceReport();
      }
      return null;
    });
    
    // Analyze results
    log('\n📊 Test Results Analysis', 'bold');
    log('=======================', 'blue');
    
    // Extension errors analysis
    const suppressedExtensionErrors = consoleMessages.filter(msg => 
      msg.text.includes('[SUPPRESSED') && 
      (msg.text.includes('runtime.lastError') || msg.text.includes('message port closed'))
    );
    
    const visibleExtensionErrors = extensionErrors.filter(error => 
      !consoleMessages.some(msg => 
        msg.text.includes('[SUPPRESSED') && msg.text.includes(error)
      )
    );
    
    if (visibleExtensionErrors.length === 0) {
      logResult('Extension Error Suppression', 'pass', 
        `${suppressedExtensionErrors.length} extension errors successfully suppressed`);
    } else {
      logResult('Extension Error Suppression', 'fail', 
        `${visibleExtensionErrors.length} extension errors still visible`);
      visibleExtensionErrors.slice(0, 3).forEach(error => 
        log(`  - ${error}`, 'red')
      );
    }
    
    // Performance violations analysis
    const slowTimeouts = performanceViolations.filter(v => v.includes('setTimeout'));
    const forcedReflows = performanceViolations.filter(v => v.includes('forced reflow'));
    
    if (performanceViolations.length === 0) {
      logResult('Performance Violations', 'pass', 'No performance violations detected');
    } else {
      logResult('Performance Violations', 'warn', 
        `${performanceViolations.length} violations detected`);
      if (slowTimeouts.length > 0) {
        log(`  - ${slowTimeouts.length} slow setTimeout operations`, 'yellow');
      }
      if (forcedReflows.length > 0) {
        log(`  - ${forcedReflows.length} forced reflow operations`, 'yellow');
      }
    }
    
    // Performance report analysis
    if (performanceReport) {
      logResult('Performance Monitoring', 'pass', 
        `${performanceReport.totalViolations} violations tracked`);
      
      if (performanceReport.summary) {
        Object.entries(performanceReport.summary).forEach(([type, data]) => {
          if (data.count > 0) {
            log(`  - ${type}: ${data.count} violations, avg ${data.avgDuration}ms`, 'yellow');
          }
        });
      }
    } else {
      logResult('Performance Monitoring', 'warn', 'Performance report not available');
    }
    
    // Runtime errors analysis
    const domErrors = runtimeErrors.filter(error => 
      error.message.includes('NotFoundError') || 
      error.message.includes('removeChild')
    );
    
    if (domErrors.length === 0) {
      logResult('DOM Manipulation Errors', 'pass', 'No DOM manipulation errors detected');
    } else {
      logResult('DOM Manipulation Errors', 'fail', 
        `${domErrors.length} DOM errors detected`);
      domErrors.slice(0, 2).forEach(error => 
        log(`  - ${error.message}`, 'red')
      );
    }
    
    // Overall assessment
    log('\n🎯 Overall Assessment', 'bold');
    log('====================', 'blue');
    
    const totalIssues = visibleExtensionErrors.length + domErrors.length;
    const performanceIssues = performanceViolations.length;
    
    if (totalIssues === 0 && performanceIssues < 3) {
      log('🎉 All critical issues resolved! Square integration is stable.', 'green');
    } else if (totalIssues === 0) {
      log('✅ Critical errors resolved. Minor performance optimizations possible.', 'yellow');
    } else {
      log(`⚠️ ${totalIssues} critical issues and ${performanceIssues} performance issues detected.`, 'red');
    }
    
    // Recommendations
    log('\n💡 Recommendations:', 'blue');
    if (visibleExtensionErrors.length > 0) {
      log('• Update extension error patterns to catch remaining errors');
    }
    if (performanceViolations.length > 5) {
      log('• Consider debouncing DOM operations and optimizing setTimeout usage');
    }
    if (domErrors.length > 0) {
      log('• Review DOM cleanup logic in Square component');
    }
    if (totalIssues === 0 && performanceIssues < 3) {
      log('• Integration is ready for production use');
    }
    
  } catch (error) {
    logResult('Test Execution', 'fail', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Main test runner
async function runPerformanceTests() {
  log('🧪 Square Performance and Error Suppression Test Suite', 'bold');
  log('====================================================', 'blue');
  
  // Check if development server is running
  try {
    const response = await fetch(BASE_URL);
    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }
    logResult('Development Server', 'pass', 'Server is running');
  } catch (error) {
    logResult('Development Server', 'fail', 'Server not accessible. Please run: npm run dev');
    return;
  }
  
  await testSquarePerformanceAndErrors();
  
  log('\n💡 Next Steps:', 'blue');
  log('1. Review any remaining performance violations');
  log('2. Check browser console for suppressed extension errors');
  log('3. Monitor Square payment processing for stability');
  log('4. Test in multiple browsers with different extensions');
}

// Run the tests
runPerformanceTests().catch(console.error);
