<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Square Performance Monitoring Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4ECDC4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45b7b8;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result.pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .result.warn { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .result.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-section {
            border-left: 4px solid #4ECDC4;
            padding-left: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 Square Performance Monitoring Fix Test</h1>
    
    <div class="test-container">
        <h2>Critical Fix Verification</h2>
        <p>This test verifies that the <code>process is not defined</code> error has been resolved in the Square performance monitoring system.</p>
        
        <div>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
            <button class="test-button" onclick="testEnvironmentDetection()">Test Environment Detection</button>
            <button class="test-button" onclick="testPerformanceMonitoring()">Test Performance Monitoring</button>
            <button class="test-button" onclick="testErrorSuppression()">Test Error Suppression</button>
            <button class="test-button" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="testResults"></div>
    </div>
    
    <div class="test-container">
        <h2>Performance Monitoring Functions</h2>
        <div class="test-section">
            <h3>Available Functions:</h3>
            <div>
                <button class="test-button" onclick="getPerformanceReport()">Get Performance Report</button>
                <button class="test-button" onclick="clearPerformanceMetrics()">Clear Metrics</button>
                <button class="test-button" onclick="testPerformanceSystem()">Test System</button>
                <button class="test-button" onclick="simulateViolation()">Simulate Violation</button>
            </div>
        </div>
        
        <div id="performanceResults"></div>
    </div>
    
    <div class="test-container">
        <h2>Console Output Monitor</h2>
        <p>This section captures console messages to verify error suppression is working:</p>
        <div>
            <button class="test-button" onclick="startConsoleMonitoring()">Start Monitoring</button>
            <button class="test-button" onclick="stopConsoleMonitoring()">Stop Monitoring</button>
            <button class="test-button" onclick="clearConsoleOutput()">Clear Output</button>
        </div>
        <div id="consoleOutput" class="console-output">Console monitoring not started...</div>
    </div>

    <script>
        let consoleMonitoring = false;
        let originalConsole = {};
        let consoleMessages = [];

        // Test results tracking
        let testResults = [];

        function addResult(test, status, message) {
            const result = { test, status, message, timestamp: new Date().toLocaleTimeString() };
            testResults.push(result);
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const container = document.getElementById('testResults');
            container.innerHTML = testResults.map(result => 
                `<div class="result ${result.status}">
                    <strong>[${result.timestamp}] ${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResultsDisplay();
            document.getElementById('performanceResults').innerHTML = '';
        }

        // Test 1: Environment Detection
        function testEnvironmentDetection() {
            try {
                // This should not throw a "process is not defined" error anymore
                if (typeof window.testSquarePerformanceMonitoring === 'function') {
                    const result = window.testSquarePerformanceMonitoring();
                    addResult('Environment Detection', 'pass', 'Environment detection working without process.env errors');
                    return true;
                } else {
                    addResult('Environment Detection', 'fail', 'testSquarePerformanceMonitoring function not available');
                    return false;
                }
            } catch (error) {
                if (error.message.includes('process is not defined')) {
                    addResult('Environment Detection', 'fail', 'CRITICAL: process is not defined error still present');
                } else {
                    addResult('Environment Detection', 'warn', `Unexpected error: ${error.message}`);
                }
                return false;
            }
        }

        // Test 2: Performance Monitoring Functions
        function testPerformanceMonitoring() {
            const functions = [
                'getSquarePerformanceReport',
                'clearSquarePerformanceMetrics',
                'testSquarePerformanceMonitoring'
            ];

            let allFunctionsAvailable = true;
            const missingFunctions = [];

            functions.forEach(funcName => {
                if (typeof window[funcName] !== 'function') {
                    allFunctionsAvailable = false;
                    missingFunctions.push(funcName);
                }
            });

            if (allFunctionsAvailable) {
                addResult('Performance Functions', 'pass', 'All performance monitoring functions available');
                
                // Test function execution
                try {
                    const report = window.getSquarePerformanceReport();
                    addResult('Function Execution', 'pass', `Performance report generated (${report.totalViolations} violations)`);
                } catch (error) {
                    addResult('Function Execution', 'fail', `Error executing functions: ${error.message}`);
                }
            } else {
                addResult('Performance Functions', 'fail', `Missing functions: ${missingFunctions.join(', ')}`);
            }

            return allFunctionsAvailable;
        }

        // Test 3: Error Suppression
        function testErrorSuppression() {
            const suppressionFunctions = [
                'debugExtensionErrors',
                'restoreConsole'
            ];

            let allFunctionsAvailable = true;
            const missingFunctions = [];

            suppressionFunctions.forEach(funcName => {
                if (typeof window[funcName] !== 'function') {
                    allFunctionsAvailable = false;
                    missingFunctions.push(funcName);
                }
            });

            if (allFunctionsAvailable) {
                addResult('Error Suppression', 'pass', 'Error suppression functions available');
            } else {
                addResult('Error Suppression', 'fail', `Missing functions: ${missingFunctions.join(', ')}`);
            }

            return allFunctionsAvailable;
        }

        // Run all tests
        function runAllTests() {
            clearResults();
            addResult('Test Suite', 'info', 'Starting comprehensive test suite...');

            const test1 = testEnvironmentDetection();
            const test2 = testPerformanceMonitoring();
            const test3 = testErrorSuppression();

            const allPassed = test1 && test2 && test3;
            
            if (allPassed) {
                addResult('Overall Result', 'pass', '🎉 All tests passed! Performance monitoring fix is successful.');
            } else {
                addResult('Overall Result', 'warn', '⚠️ Some tests failed. Review individual test results above.');
            }
        }

        // Performance monitoring utilities
        function getPerformanceReport() {
            try {
                const report = window.getSquarePerformanceReport();
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result info">
                        <strong>Performance Report:</strong><br>
                        <pre>${JSON.stringify(report, null, 2)}</pre>
                    </div>`;
            } catch (error) {
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result fail">Error getting performance report: ${error.message}</div>`;
            }
        }

        function clearPerformanceMetrics() {
            try {
                window.clearSquarePerformanceMetrics();
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result pass">Performance metrics cleared successfully</div>`;
            } catch (error) {
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result fail">Error clearing metrics: ${error.message}</div>`;
            }
        }

        function testPerformanceSystem() {
            try {
                const result = window.testSquarePerformanceMonitoring();
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result pass">
                        <strong>Performance System Test:</strong><br>
                        <pre>${JSON.stringify(result, null, 2)}</pre>
                    </div>`;
            } catch (error) {
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result fail">Error testing performance system: ${error.message}</div>`;
            }
        }

        function simulateViolation() {
            try {
                // Simulate a slow operation to trigger performance monitoring
                const start = performance.now();
                
                // Create a slow setTimeout to trigger violation detection
                setTimeout(() => {
                    // Simulate slow work
                    let result = 0;
                    for (let i = 0; i < 1000000; i++) {
                        result += Math.random();
                    }
                    
                    const duration = performance.now() - start;
                    document.getElementById('performanceResults').innerHTML = 
                        `<div class="result info">
                            Simulated slow operation completed in ${Math.round(duration)}ms<br>
                            Check performance report for recorded violations.
                        </div>`;
                }, 200); // 200ms delay to potentially trigger violation
                
            } catch (error) {
                document.getElementById('performanceResults').innerHTML = 
                    `<div class="result fail">Error simulating violation: ${error.message}</div>`;
            }
        }

        // Console monitoring
        function startConsoleMonitoring() {
            if (consoleMonitoring) return;
            
            consoleMonitoring = true;
            consoleMessages = [];
            
            // Store original console methods
            originalConsole = {
                log: console.log,
                warn: console.warn,
                error: console.error
            };
            
            // Override console methods to capture messages
            ['log', 'warn', 'error'].forEach(method => {
                console[method] = function(...args) {
                    const message = args.map(arg => 
                        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                    ).join(' ');
                    
                    consoleMessages.push({
                        type: method,
                        message: message,
                        timestamp: new Date().toLocaleTimeString()
                    });
                    
                    updateConsoleOutput();
                    
                    // Call original method
                    originalConsole[method].apply(console, args);
                };
            });
            
            document.getElementById('consoleOutput').innerHTML = 'Console monitoring started...\n';
        }

        function stopConsoleMonitoring() {
            if (!consoleMonitoring) return;
            
            consoleMonitoring = false;
            
            // Restore original console methods
            Object.keys(originalConsole).forEach(method => {
                console[method] = originalConsole[method];
            });
            
            updateConsoleOutput();
        }

        function updateConsoleOutput() {
            const output = consoleMessages.map(msg => 
                `[${msg.timestamp}] ${msg.type.toUpperCase()}: ${msg.message}`
            ).join('\n');
            
            document.getElementById('consoleOutput').innerHTML = 
                output + (consoleMonitoring ? '\n\n[Monitoring active...]' : '\n\n[Monitoring stopped]');
        }

        function clearConsoleOutput() {
            consoleMessages = [];
            document.getElementById('consoleOutput').innerHTML = 
                consoleMonitoring ? 'Console monitoring active...\n' : 'Console monitoring not started...';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('Page Load', 'info', 'Performance monitoring fix test page loaded');
                testEnvironmentDetection();
            }, 1000);
        });
    </script>
</body>
</html>
