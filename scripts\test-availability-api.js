/**
 * Test script for the booking availability API endpoint
 * 
 * This script tests the new /api/bookings/availability endpoint
 * to ensure it works correctly with the POS workflow.
 */

const fetch = require('node-fetch');

// Configuration
const config = {
  baseUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  testServiceId: '123e4567-e89b-12d3-a456-426614174000', // Replace with actual service ID
  testDate: new Date().toISOString().split('T')[0], // Today's date
  testDuration: 30
};

// Test authentication token (you'll need to get this from your admin login)
const testToken = process.env.TEST_ADMIN_TOKEN || '';

/**
 * Test the availability API endpoint
 */
async function testAvailabilityAPI() {
  console.log('🧪 Testing Booking Availability API');
  console.log('=====================================');
  
  if (!testToken) {
    console.log('⚠️  Warning: No TEST_ADMIN_TOKEN provided. Some tests may fail.');
    console.log('   To get a token, log in to the admin panel and check browser dev tools.');
  }

  // Test 1: Basic availability check
  console.log('\n📅 Test 1: Basic availability check');
  try {
    const url = `${config.baseUrl}/api/bookings/availability?date=${config.testDate}&service_id=${config.testServiceId}&duration=${config.testDuration}`;
    console.log(`   URL: ${url}`);
    
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('   ✅ Success!');
      console.log(`   📊 Total slots: ${data.total_slots}`);
      console.log(`   ✅ Available slots: ${data.available_slots}`);
      console.log(`   ❌ Booked slots: ${data.booked_slots}`);
      console.log(`   👥 Available artists: ${data.available_artists?.length || 0}`);
      
      // Show first few available slots
      const availableSlots = data.availability?.filter(slot => slot.available).slice(0, 3);
      if (availableSlots?.length > 0) {
        console.log('   🕐 Sample available slots:');
        availableSlots.forEach(slot => {
          const startTime = new Date(slot.start_time).toLocaleTimeString();
          const endTime = new Date(slot.end_time).toLocaleTimeString();
          console.log(`      ${startTime} - ${endTime}`);
        });
      }
    } else {
      const errorData = await response.text();
      console.log('   ❌ Failed!');
      console.log(`   Error: ${errorData}`);
    }
  } catch (error) {
    console.log('   ❌ Request failed!');
    console.log(`   Error: ${error.message}`);
  }

  // Test 2: Missing parameters
  console.log('\n🚫 Test 2: Missing parameters validation');
  try {
    const url = `${config.baseUrl}/api/bookings/availability`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${response.status}`);
    
    if (response.status === 400) {
      const data = await response.json();
      console.log('   ✅ Correctly rejected missing parameters');
      console.log(`   Error message: ${data.error}`);
    } else {
      console.log('   ❌ Should have returned 400 for missing parameters');
    }
  } catch (error) {
    console.log('   ❌ Request failed!');
    console.log(`   Error: ${error.message}`);
  }

  // Test 3: Invalid date format
  console.log('\n📅 Test 3: Invalid date format validation');
  try {
    const url = `${config.baseUrl}/api/bookings/availability?date=invalid-date&service_id=${config.testServiceId}`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Status: ${response.status}`);
    
    if (response.status === 400) {
      const data = await response.json();
      console.log('   ✅ Correctly rejected invalid date format');
      console.log(`   Error message: ${data.error}`);
    } else {
      console.log('   ❌ Should have returned 400 for invalid date');
    }
  } catch (error) {
    console.log('   ❌ Request failed!');
    console.log(`   Error: ${error.message}`);
  }

  // Test 4: Future date
  console.log('\n🔮 Test 4: Future date availability');
  try {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 7); // Next week
    const futureDateStr = futureDate.toISOString().split('T')[0];
    
    const url = `${config.baseUrl}/api/bookings/availability?date=${futureDateStr}&service_id=${config.testServiceId}&duration=${config.testDuration}`;
    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`   Date: ${futureDateStr}`);
    console.log(`   Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('   ✅ Success!');
      console.log(`   📊 Total slots: ${data.total_slots}`);
      console.log(`   ✅ Available slots: ${data.available_slots}`);
      console.log('   💡 Future dates should typically have more availability');
    } else {
      const errorData = await response.text();
      console.log('   ❌ Failed!');
      console.log(`   Error: ${errorData}`);
    }
  } catch (error) {
    console.log('   ❌ Request failed!');
    console.log(`   Error: ${error.message}`);
  }

  console.log('\n🏁 Testing complete!');
  console.log('\n💡 Tips for integration:');
  console.log('   - The API returns time slots in 15-minute intervals');
  console.log('   - Slots are marked as available/unavailable based on existing bookings');
  console.log('   - Past time slots (with 1-hour grace period) are filtered out');
  console.log('   - Artist assignments are included when available');
  console.log('   - The response includes summary statistics for easy UI updates');
}

/**
 * Test the API with real service data
 */
async function testWithRealServices() {
  console.log('\n🔍 Fetching real services for testing...');
  
  try {
    const response = await fetch(`${config.baseUrl}/api/admin/services`, {
      headers: {
        'Authorization': `Bearer ${testToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      const services = data.services || data;
      
      if (services && services.length > 0) {
        const firstService = services[0];
        console.log(`   Found service: ${firstService.name} (ID: ${firstService.id})`);
        
        // Update config with real service ID
        config.testServiceId = firstService.id;
        config.testDuration = firstService.duration || 30;
        
        console.log(`   Using duration: ${config.testDuration} minutes`);
        
        // Run the availability test with real data
        await testAvailabilityAPI();
      } else {
        console.log('   ⚠️  No services found. Using default test service ID.');
        await testAvailabilityAPI();
      }
    } else {
      console.log('   ⚠️  Could not fetch services. Using default test service ID.');
      await testAvailabilityAPI();
    }
  } catch (error) {
    console.log('   ⚠️  Error fetching services. Using default test service ID.');
    console.log(`   Error: ${error.message}`);
    await testAvailabilityAPI();
  }
}

// Run the tests
if (require.main === module) {
  testWithRealServices().catch(console.error);
}

module.exports = {
  testAvailabilityAPI,
  testWithRealServices
};
