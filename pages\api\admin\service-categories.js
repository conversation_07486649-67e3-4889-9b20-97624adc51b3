import { supabaseAdmin } from '../../../lib/supabase';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Service Categories API - Fetching categories');

    // Fetch service categories
    const { data: categories, error } = await supabaseAdmin
      .from('service_categories')
      .select('id, name, description, parent_id')
      .order('name');

    if (error) {
      console.error('❌ Service Categories API - Database error:', error);
      throw error;
    }

    console.log(`✅ Service Categories API - Found ${categories?.length || 0} categories`);

    // Apply EXACT working pattern from services API - convert ALL data to primitive strings
    // This is the proven method that prevents React Error #130
    const serializedCategories = categories?.map(category => ({
      id: String(category.id || ''),
      name: String(category.name || ''),
      description: String(category.description || ''),
      parent_id: category.parent_id ? String(category.parent_id) : null
    })) || [];

    res.status(200).json({
      success: true,
      categories: serializedCategories
    });

  } catch (error) {
    console.error('💥 Service Categories API - Error:', error);
    res.status(500).json({
      error: 'Failed to fetch service categories',
      details: error.message
    });
  }
}
