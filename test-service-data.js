// Test script to fetch and analyze service data structure
const fetch = require('node-fetch');

async function analyzeServiceData() {
    try {
        console.log('🔍 Fetching service data from admin API...');
        
        const response = await fetch('http://localhost:3001/api/admin/services?sort_by=name&sort_order=asc', {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer dev-token',
                'x-auth-token': 'dev-token'
            }
        });
        
        if (!response.ok) {
            console.error('❌ Failed to fetch services:', response.status, response.statusText);
            return;
        }
        
        const services = await response.json();
        console.log(`✅ Fetched ${services.length} services`);
        
        if (services.length > 0) {
            const firstService = services[0];
            console.log('\n📊 ANALYZING FIRST SERVICE STRUCTURE:');
            console.log('=====================================');
            
            for (const [key, value] of Object.entries(firstService)) {
                const type = typeof value;
                const isArray = Array.isArray(value);
                const isObject = type === 'object' && value !== null && !isArray;
                const isDate = value instanceof Date;
                
                console.log(`${key}: ${type}${isArray ? ' (array)' : ''}${isObject ? ' (object)' : ''}${isDate ? ' (date)' : ''}`);
                
                if (isObject && !isDate) {
                    console.log(`  ❌ POTENTIAL PROBLEM: ${key} is an object:`, JSON.stringify(value).substring(0, 100) + '...');
                } else if (isArray && value.length > 0 && typeof value[0] === 'object') {
                    console.log(`  ❌ POTENTIAL PROBLEM: ${key} is an array of objects:`, JSON.stringify(value[0]).substring(0, 100) + '...');
                } else if (isDate) {
                    console.log(`  ⚠️ WARNING: ${key} is a Date object:`, value.toISOString());
                }
            }
            
            console.log('\n📋 FULL FIRST SERVICE OBJECT:');
            console.log('==============================');
            console.log(JSON.stringify(firstService, null, 2));
            
            // Check pricing_tiers specifically
            if (firstService.pricing_tiers) {
                console.log('\n🎯 PRICING TIERS ANALYSIS:');
                console.log('===========================');
                console.log('Type:', typeof firstService.pricing_tiers);
                console.log('Is Array:', Array.isArray(firstService.pricing_tiers));
                console.log('Length:', firstService.pricing_tiers.length);
                
                if (Array.isArray(firstService.pricing_tiers) && firstService.pricing_tiers.length > 0) {
                    console.log('First tier structure:');
                    for (const [key, value] of Object.entries(firstService.pricing_tiers[0])) {
                        console.log(`  ${key}: ${typeof value} = ${value}`);
                    }
                }
            }
        }
        
    } catch (error) {
        console.error('❌ Error analyzing service data:', error.message);
    }
}

analyzeServiceData();
