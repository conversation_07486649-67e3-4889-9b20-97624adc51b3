#!/usr/bin/env node

/**
 * Square Payment Integration Test Script
 * Tests all aspects of Square payment configuration and functionality
 */

import https from 'https';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const BASE_URL = 'http://localhost:3000';
const SQUARE_SANDBOX_URL = 'https://connect.squareupsandbox.com';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logResult(test, status, details = '') {
  const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
  const color = status === 'pass' ? 'green' : status === 'fail' ? 'red' : 'yellow';
  log(`${icon} ${test}: ${details}`, color);
}

// Test environment variables
function testEnvironmentVariables() {
  log('\n🔧 Testing Environment Variables...', 'blue');
  
  const envFile = path.join(__dirname, '.env.local');
  if (!fs.existsSync(envFile)) {
    logResult('Environment File', 'fail', '.env.local file not found');
    return false;
  }
  
  logResult('Environment File', 'pass', '.env.local file exists');
  
  const envContent = fs.readFileSync(envFile, 'utf8');
  const requiredVars = [
    'NEXT_PUBLIC_SQUARE_APPLICATION_ID',
    'NEXT_PUBLIC_SQUARE_LOCATION_ID',
    'SQUARE_ACCESS_TOKEN',
    'SQUARE_ENVIRONMENT'
  ];
  
  let allVarsPresent = true;
  requiredVars.forEach(varName => {
    if (envContent.includes(`${varName}=`)) {
      logResult(`Environment Variable: ${varName}`, 'pass', 'Present');
    } else {
      logResult(`Environment Variable: ${varName}`, 'fail', 'Missing');
      allVarsPresent = false;
    }
  });
  
  return allVarsPresent;
}

// Test CSP configuration
function testCSPConfiguration() {
  log('\n🛡️ Testing CSP Configuration...', 'blue');
  
  const configFile = path.join(__dirname, 'next.config.js');
  if (!fs.existsSync(configFile)) {
    logResult('Next.js Config', 'fail', 'next.config.js not found');
    return false;
  }
  
  const configContent = fs.readFileSync(configFile, 'utf8');
  
  const requiredDomains = [
    'sandbox.web.squarecdn.com',
    'web.squarecdn.com',
    'pci-connect.squareupsandbox.com',
    'pci-connect.squareup.com',
    'square-fonts-production-f.squarecdn.com',
    'd1g145x70srn7h.cloudfront.net'
  ];
  
  let allDomainsPresent = true;
  requiredDomains.forEach(domain => {
    if (configContent.includes(domain)) {
      logResult(`CSP Domain: ${domain}`, 'pass', 'Whitelisted');
    } else {
      logResult(`CSP Domain: ${domain}`, 'fail', 'Not found in CSP');
      allDomainsPresent = false;
    }
  });
  
  return allDomainsPresent;
}

// Test Square API connectivity
async function testSquareAPIConnectivity() {
  log('\n🌐 Testing Square API Connectivity...', 'blue');
  
  try {
    const envFile = path.join(__dirname, '.env.local');
    const envContent = fs.readFileSync(envFile, 'utf8');
    
    // Extract access token
    const tokenMatch = envContent.match(/SQUARE_ACCESS_TOKEN=(.+)/);
    if (!tokenMatch) {
      logResult('Square API Test', 'fail', 'Access token not found');
      return false;
    }
    
    const accessToken = tokenMatch[1].trim();
    
    // Test Square API with locations endpoint
    const response = await fetch(`${SQUARE_SANDBOX_URL}/v2/locations`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Square-Version': '2023-10-18',
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      logResult('Square API Connectivity', 'pass', `Connected successfully (${data.locations?.length || 0} locations)`);
      return true;
    } else {
      logResult('Square API Connectivity', 'fail', `HTTP ${response.status}: ${response.statusText}`);
      return false;
    }
  } catch (error) {
    logResult('Square API Connectivity', 'fail', error.message);
    return false;
  }
}

// Test local server endpoints
async function testLocalEndpoints() {
  log('\n🖥️ Testing Local Server Endpoints...', 'blue');
  
  const endpoints = [
    '/api/admin/pos/process-payment',
    '/api/admin/test-connections'
  ];
  
  let allEndpointsWorking = true;
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'GET'
      });
      
      if (response.status === 405) {
        // Method not allowed is expected for POST-only endpoints
        logResult(`Endpoint: ${endpoint}`, 'pass', 'Endpoint exists (405 Method Not Allowed)');
      } else if (response.status === 401) {
        // Unauthorized is expected for protected endpoints
        logResult(`Endpoint: ${endpoint}`, 'pass', 'Endpoint exists (401 Unauthorized)');
      } else if (response.ok) {
        logResult(`Endpoint: ${endpoint}`, 'pass', `HTTP ${response.status}`);
      } else {
        logResult(`Endpoint: ${endpoint}`, 'warn', `HTTP ${response.status}`);
      }
    } catch (error) {
      logResult(`Endpoint: ${endpoint}`, 'fail', error.message);
      allEndpointsWorking = false;
    }
  }
  
  return allEndpointsWorking;
}

// Test Square SDK loading
async function testSquareSDKLoading() {
  log('\n📦 Testing Square SDK Loading...', 'blue');
  
  try {
    const response = await fetch('https://sandbox.web.squarecdn.com/v1/square.js');
    if (response.ok) {
      logResult('Square SDK Availability', 'pass', 'SDK script accessible');
      return true;
    } else {
      logResult('Square SDK Availability', 'fail', `HTTP ${response.status}`);
      return false;
    }
  } catch (error) {
    logResult('Square SDK Availability', 'fail', error.message);
    return false;
  }
}

// Main test runner
async function runTests() {
  log('🧪 Square Payment Integration Test Suite', 'bold');
  log('==========================================', 'blue');
  
  const results = {
    envVars: testEnvironmentVariables(),
    cspConfig: testCSPConfiguration(),
    squareAPI: await testSquareAPIConnectivity(),
    localEndpoints: await testLocalEndpoints(),
    squareSDK: await testSquareSDKLoading()
  };
  
  // Summary
  log('\n📊 Test Summary', 'bold');
  log('================', 'blue');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(Boolean).length;
  
  log(`Total Tests: ${totalTests}`);
  log(`Passed: ${passedTests}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`Failed: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'green' : 'red');
  
  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! Square payment integration is ready.', 'green');
  } else {
    log('\n⚠️ Some tests failed. Please review the issues above.', 'yellow');
  }
  
  // Recommendations
  log('\n💡 Next Steps:', 'blue');
  log('1. Navigate to http://localhost:3000/admin/pos');
  log('2. Complete a test transaction using sandbox card: 4111 1111 1111 1111');
  log('3. Check browser console for any CSP violations or errors');
  log('4. Verify payment processing in the admin panel');
}

// Run the tests
runTests().catch(console.error);
