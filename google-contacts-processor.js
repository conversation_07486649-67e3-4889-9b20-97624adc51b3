const fs = require('fs');
const csv = require('csv-parser');
const { createObjectCsvWriter } = require('csv-writer');

/**
 * Google Contacts CSV Processor for Ocean Soul Sparkles
 * Processes Google Contacts export and converts to Supabase customer format
 * Focuses on customers with Square Customer IDs for payment integration
 */

class GoogleContactsProcessor {
  constructor() {
    this.contacts = [];
    this.processedCustomers = [];
    this.duplicates = [];
    this.statistics = {
      totalContacts: 0,
      contactsWithSquareId: 0,
      validEmails: 0,
      validPhones: 0,
      duplicatesFound: 0,
      dataQualityIssues: []
    };
  }

  /**
   * Main processing function
   */
  async processGoogleContacts() {
    console.log('🚀 Starting Google Contacts processing...');
    
    try {
      // Step 1: Read and parse CSV
      await this.readCSV();
      
      // Step 2: Filter contacts with Square Customer IDs
      this.filterSquareCustomers();
      
      // Step 3: Clean and standardize data
      this.cleanData();
      
      // Step 4: Detect and handle duplicates
      this.detectDuplicates();
      
      // Step 5: Convert to Supabase format
      this.convertToSupabaseFormat();
      
      // Step 6: Generate outputs
      await this.generateOutputs();
      
      console.log('✅ Processing completed successfully!');
      this.printSummary();
      
    } catch (error) {
      console.error('❌ Error processing contacts:', error);
      throw error;
    }
  }

  /**
   * Read CSV file and parse contacts
   */
  async readCSV() {
    return new Promise((resolve, reject) => {
      console.log('📖 Reading Google Contacts CSV...');
      
      fs.createReadStream('contacts .Google csv.csv')
        .pipe(csv())
        .on('data', (row) => {
          this.contacts.push(row);
        })
        .on('end', () => {
          this.statistics.totalContacts = this.contacts.length;
          console.log(`📊 Loaded ${this.contacts.length} contacts`);
          resolve();
        })
        .on('error', reject);
    });
  }

  /**
   * Filter contacts that have Square Customer IDs
   */
  filterSquareCustomers() {
    console.log('🔍 Filtering contacts with Square Customer IDs...');
    
    const squareCustomers = this.contacts.filter(contact => {
      const squareId = contact['Custom Field 9 - Value'] || contact['Square Customer ID'];
      return squareId && squareId.trim() !== '';
    });
    
    this.contacts = squareCustomers;
    this.statistics.contactsWithSquareId = squareCustomers.length;
    console.log(`💳 Found ${squareCustomers.length} contacts with Square Customer IDs`);
  }

  /**
   * Clean and standardize contact data
   */
  cleanData() {
    console.log('🧹 Cleaning and standardizing data...');
    
    this.contacts.forEach((contact, index) => {
      try {
        // Clean name
        contact.cleanName = this.cleanName(contact);
        
        // Clean email
        contact.cleanEmail = this.cleanEmail(contact);
        
        // Clean phone
        contact.cleanPhone = this.cleanPhone(contact);
        
        // Clean address
        contact.cleanAddress = this.cleanAddress(contact);
        
        // Extract custom fields
        contact.customFields = this.extractCustomFields(contact);
        
      } catch (error) {
        this.statistics.dataQualityIssues.push({
          index,
          issue: `Data cleaning error: ${error.message}`,
          contact: contact['First Name'] + ' ' + contact['Last Name']
        });
      }
    });
  }

  /**
   * Clean and combine first/last name
   */
  cleanName(contact) {
    const firstName = (contact['First Name'] || '').trim();
    const lastName = (contact['Last Name'] || '').trim();
    
    if (!firstName && !lastName) {
      // Use organization name if no personal name
      return (contact['Organization Name'] || 'Unknown').trim();
    }
    
    return `${firstName} ${lastName}`.trim();
  }

  /**
   * Clean and validate email
   */
  cleanEmail(contact) {
    const email = (contact['E-mail 1 - Value'] || '').trim().toLowerCase();
    
    if (!email) {
      this.statistics.dataQualityIssues.push({
        issue: 'Missing email',
        contact: contact['First Name'] + ' ' + contact['Last Name']
      });
      return null;
    }
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      this.statistics.dataQualityIssues.push({
        issue: `Invalid email format: ${email}`,
        contact: contact['First Name'] + ' ' + contact['Last Name']
      });
      return null;
    }
    
    this.statistics.validEmails++;
    return email;
  }

  /**
   * Clean and standardize phone number to Australian format
   */
  cleanPhone(contact) {
    let phone = contact['Phone 1 - Value'] || '';
    
    if (!phone || phone.trim() === '') {
      return null;
    }
    
    // Remove all non-digit characters except +
    phone = phone.replace(/[^\d+]/g, '');
    
    // Handle different Australian phone formats
    if (phone.startsWith('+61')) {
      // Already in international format
      this.statistics.validPhones++;
      return phone;
    } else if (phone.startsWith('61') && phone.length === 11) {
      // Missing + prefix
      this.statistics.validPhones++;
      return '+' + phone;
    } else if (phone.startsWith('04') && phone.length === 10) {
      // Australian mobile format
      this.statistics.validPhones++;
      return '+61' + phone.substring(1);
    } else if (phone.startsWith('0') && phone.length === 10) {
      // Australian landline format
      this.statistics.validPhones++;
      return '+61' + phone.substring(1);
    } else {
      this.statistics.dataQualityIssues.push({
        issue: `Invalid phone format: ${contact['Phone 1 - Value']}`,
        contact: contact['First Name'] + ' ' + contact['Last Name']
      });
      return contact['Phone 1 - Value']; // Return original if can't parse
    }
  }

  /**
   * Clean and parse address information
   */
  cleanAddress(contact) {
    const address = {
      street: (contact['Address 1 - Street'] || '').trim(),
      city: (contact['Address 1 - City'] || '').trim(),
      state: (contact['Address 1 - Region'] || '').trim(),
      postal_code: (contact['Address 1 - Postal Code'] || '').trim(),
      country: (contact['Address 1 - Country'] || 'Australia').trim(),
      formatted: (contact['Address 1 - Formatted'] || '').trim()
    };
    
    // If no structured address but formatted address exists, try to parse it
    if (!address.street && !address.city && address.formatted) {
      const parsed = this.parseFormattedAddress(address.formatted);
      return { ...address, ...parsed };
    }
    
    return address;
  }

  /**
   * Parse formatted address string
   */
  parseFormattedAddress(formatted) {
    const lines = formatted.split('\n').map(line => line.trim()).filter(line => line);
    const result = {};
    
    if (lines.length >= 2) {
      result.street = lines[0];
      
      // Last line usually contains city, state, postal code
      const lastLine = lines[lines.length - 1];
      if (lastLine.includes('Australia')) {
        result.country = 'Australia';
        const parts = lastLine.replace('Australia', '').trim().split(',');
        if (parts.length >= 2) {
          result.city = parts[0].trim();
          const statePostal = parts[1].trim().split(' ');
          if (statePostal.length >= 2) {
            result.state = statePostal[0];
            result.postal_code = statePostal[1];
          }
        }
      }
    }
    
    return result;
  }

  /**
   * Extract custom fields relevant to business
   */
  extractCustomFields(contact) {
    const fields = {};
    
    // Map custom fields by their labels
    for (let i = 1; i <= 16; i++) {
      const label = contact[`Custom Field ${i} - Label`];
      const value = contact[`Custom Field ${i} - Value`];
      
      if (label && value) {
        fields[label] = value;
      }
    }
    
    return fields;
  }

  /**
   * Detect duplicate customers
   */
  detectDuplicates() {
    console.log('🔍 Detecting duplicate customers...');

    const emailMap = new Map();
    const phoneMap = new Map();
    const duplicateGroups = [];

    this.contacts.forEach((contact, index) => {
      const email = contact.cleanEmail;
      const phone = contact.cleanPhone;

      // Check for email duplicates
      if (email) {
        if (emailMap.has(email)) {
          const existingIndex = emailMap.get(email);
          duplicateGroups.push({
            type: 'email',
            email,
            contacts: [existingIndex, index]
          });
        } else {
          emailMap.set(email, index);
        }
      }

      // Check for phone duplicates
      if (phone) {
        if (phoneMap.has(phone)) {
          const existingIndex = phoneMap.get(phone);
          duplicateGroups.push({
            type: 'phone',
            phone,
            contacts: [existingIndex, index]
          });
        } else {
          phoneMap.set(phone, index);
        }
      }
    });

    this.duplicates = duplicateGroups;
    this.statistics.duplicatesFound = duplicateGroups.length;
    console.log(`🔄 Found ${duplicateGroups.length} potential duplicate groups`);
  }

  /**
   * Convert contacts to Supabase customer format
   */
  convertToSupabaseFormat() {
    console.log('🔄 Converting to Supabase customer format...');

    this.processedCustomers = this.contacts.map((contact, index) => {
      const customFields = contact.customFields || {};

      // Determine marketing consent
      const emailSubscriberStatus = customFields['Email subscriber status'] || '';
      const marketingConsent = emailSubscriberStatus.toLowerCase() === 'subscribed';

      // Build notes with Square and business data
      const notes = this.buildCustomerNotes(contact, customFields);

      // Convert to Supabase format
      const customer = {
        name: contact.cleanName,
        email: contact.cleanEmail,
        phone: contact.cleanPhone,
        address: contact.cleanAddress?.street || null,
        city: contact.cleanAddress?.city || null,
        state: contact.cleanAddress?.state || null,
        postal_code: contact.cleanAddress?.postal_code || null,
        country: contact.cleanAddress?.country || 'Australia',
        notes: notes,
        marketing_consent: marketingConsent,
        // Additional fields for enhanced customer data
        square_customer_id: customFields['Square Customer ID'] || null,
        created_at: this.parseDate(customFields['Created At']),
        acquisition_source: customFields['Creation Source'] || 'Google Contacts Import',
        transaction_count: this.parseNumber(customFields['Transaction Count']),
        total_spend: this.parseNumber(customFields['Total Spend']),
        first_visit: this.parseDate(customFields['First Visit']),
        last_visit: this.parseDate(customFields['Last Visit']),
        last_visit_square: this.parseDate(customFields['Last Visit SQUARE'])
      };

      // Remove null/undefined values
      Object.keys(customer).forEach(key => {
        if (customer[key] === null || customer[key] === undefined || customer[key] === '') {
          delete customer[key];
        }
      });

      return customer;
    }).filter(customer => customer.email); // Only include customers with valid emails

    console.log(`✅ Converted ${this.processedCustomers.length} customers to Supabase format`);
  }

  /**
   * Build comprehensive notes field with Square and business data
   */
  buildCustomerNotes(contact, customFields) {
    const notes = [];

    // Add original notes if they exist
    if (contact.Notes && contact.Notes.trim()) {
      notes.push(`Original Notes: ${contact.Notes.trim()}`);
    }

    // Add Square Customer ID
    if (customFields['Square Customer ID']) {
      notes.push(`Square Customer ID: ${customFields['Square Customer ID']}`);
    }

    // Add transaction data
    if (customFields['Transaction Count']) {
      notes.push(`Transaction Count: ${customFields['Transaction Count']}`);
    }

    if (customFields['Total Spend']) {
      notes.push(`Total Spend: ${customFields['Total Spend']}`);
    }

    // Add visit dates
    if (customFields['First Visit']) {
      notes.push(`First Visit: ${customFields['First Visit']}`);
    }

    if (customFields['Last Visit SQUARE']) {
      notes.push(`Last Square Visit: ${customFields['Last Visit SQUARE']}`);
    }

    // Add subscription status
    if (customFields['Email subscriber status']) {
      notes.push(`Email Subscription: ${customFields['Email subscriber status']}`);
    }

    if (customFields['SMS subscriber status']) {
      notes.push(`SMS Subscription: ${customFields['SMS subscriber status']}`);
    }

    // Add memo field (contains merge information)
    if (customFields['Memo']) {
      notes.push(`Memo: ${customFields['Memo']}`);
    }

    // Add import metadata
    notes.push(`Imported from Google Contacts: ${new Date().toISOString()}`);

    return notes.join('\n\n');
  }

  /**
   * Parse date string to ISO format
   */
  parseDate(dateString) {
    if (!dateString || dateString.trim() === '') {
      return null;
    }

    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? null : date.toISOString();
    } catch (error) {
      return null;
    }
  }

  /**
   * Parse number string to float
   */
  parseNumber(numberString) {
    if (!numberString || numberString.trim() === '') {
      return null;
    }

    const number = parseFloat(numberString);
    return isNaN(number) ? null : number;
  }

  /**
   * Generate output files
   */
  async generateOutputs() {
    console.log('📄 Generating output files...');

    // Generate processed customers CSV
    await this.generateCustomersCSV();

    // Generate analysis report
    await this.generateAnalysisReport();

    // Generate duplicates report
    await this.generateDuplicatesReport();

    console.log('📁 Output files generated successfully');
  }

  /**
   * Generate CSV file with processed customers
   */
  async generateCustomersCSV() {
    const csvWriter = createObjectCsvWriter({
      path: 'processed-customers.csv',
      header: [
        { id: 'name', title: 'Name' },
        { id: 'email', title: 'Email' },
        { id: 'phone', title: 'Phone' },
        { id: 'address', title: 'Address' },
        { id: 'city', title: 'City' },
        { id: 'state', title: 'State' },
        { id: 'postal_code', title: 'Postal Code' },
        { id: 'country', title: 'Country' },
        { id: 'marketing_consent', title: 'Marketing Consent' },
        { id: 'square_customer_id', title: 'Square Customer ID' },
        { id: 'created_at', title: 'Created At' },
        { id: 'acquisition_source', title: 'Acquisition Source' },
        { id: 'transaction_count', title: 'Transaction Count' },
        { id: 'total_spend', title: 'Total Spend' },
        { id: 'first_visit', title: 'First Visit' },
        { id: 'last_visit_square', title: 'Last Square Visit' },
        { id: 'notes', title: 'Notes' }
      ]
    });

    await csvWriter.writeRecords(this.processedCustomers);
    console.log('✅ Generated processed-customers.csv');
  }

  /**
   * Generate analysis report
   */
  async generateAnalysisReport() {
    const report = {
      summary: {
        totalContactsProcessed: this.statistics.totalContacts,
        contactsWithSquareId: this.statistics.contactsWithSquareId,
        validCustomersGenerated: this.processedCustomers.length,
        validEmails: this.statistics.validEmails,
        validPhones: this.statistics.validPhones,
        duplicatesFound: this.statistics.duplicatesFound,
        dataQualityIssues: this.statistics.dataQualityIssues.length
      },
      dataQualityIssues: this.statistics.dataQualityIssues,
      recommendations: this.generateRecommendations(),
      processingDate: new Date().toISOString()
    };

    fs.writeFileSync('import-analysis.json', JSON.stringify(report, null, 2));
    console.log('✅ Generated import-analysis.json');
  }

  /**
   * Generate duplicates report
   */
  async generateDuplicatesReport() {
    const duplicatesReport = {
      summary: {
        totalDuplicateGroups: this.duplicates.length,
        requiresManualReview: this.duplicates.length > 0
      },
      duplicateGroups: this.duplicates.map(group => ({
        ...group,
        contactDetails: group.contacts.map(index => ({
          index,
          name: this.contacts[index]?.cleanName,
          email: this.contacts[index]?.cleanEmail,
          phone: this.contacts[index]?.cleanPhone,
          squareId: this.contacts[index]?.customFields?.['Square Customer ID']
        }))
      })),
      processingDate: new Date().toISOString()
    };

    fs.writeFileSync('duplicate-customers.json', JSON.stringify(duplicatesReport, null, 2));
    console.log('✅ Generated duplicate-customers.json');
  }

  /**
   * Generate recommendations for database integration
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.statistics.dataQualityIssues.length > 0) {
      recommendations.push({
        priority: 'HIGH',
        category: 'Data Quality',
        issue: `${this.statistics.dataQualityIssues.length} data quality issues found`,
        action: 'Review and clean data before import. Check import-analysis.json for details.'
      });
    }

    if (this.statistics.duplicatesFound > 0) {
      recommendations.push({
        priority: 'HIGH',
        category: 'Duplicates',
        issue: `${this.statistics.duplicatesFound} potential duplicate groups found`,
        action: 'Review duplicate-customers.json and merge or remove duplicates before import.'
      });
    }

    if (this.statistics.validEmails < this.statistics.contactsWithSquareId) {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'Email Validation',
        issue: 'Some contacts have invalid or missing email addresses',
        action: 'Consider manual review of contacts without valid emails.'
      });
    }

    if (this.statistics.validPhones < this.statistics.contactsWithSquareId) {
      recommendations.push({
        priority: 'LOW',
        category: 'Phone Validation',
        issue: 'Some contacts have invalid or missing phone numbers',
        action: 'Consider updating phone numbers for better customer communication.'
      });
    }

    recommendations.push({
      priority: 'INFO',
      category: 'Import Strategy',
      issue: 'Database integration approach',
      action: 'Use processed-customers.csv for bulk import or create individual API calls for each customer.'
    });

    return recommendations;
  }

  /**
   * Print processing summary
   */
  printSummary() {
    console.log('\n📊 PROCESSING SUMMARY');
    console.log('='.repeat(50));
    console.log(`📋 Total contacts loaded: ${this.statistics.totalContacts}`);
    console.log(`💳 Contacts with Square IDs: ${this.statistics.contactsWithSquareId}`);
    console.log(`✅ Valid customers generated: ${this.processedCustomers.length}`);
    console.log(`📧 Valid emails: ${this.statistics.validEmails}`);
    console.log(`📱 Valid phones: ${this.statistics.validPhones}`);
    console.log(`🔄 Duplicate groups: ${this.statistics.duplicatesFound}`);
    console.log(`⚠️  Data quality issues: ${this.statistics.dataQualityIssues.length}`);

    console.log('\n📁 OUTPUT FILES GENERATED:');
    console.log('• processed-customers.csv - Cleaned customer data ready for import');
    console.log('• import-analysis.json - Detailed analysis and recommendations');
    console.log('• duplicate-customers.json - Duplicate detection report');

    if (this.statistics.dataQualityIssues.length > 0) {
      console.log('\n⚠️  ATTENTION REQUIRED:');
      console.log('Data quality issues found. Review import-analysis.json before proceeding.');
    }

    if (this.statistics.duplicatesFound > 0) {
      console.log('\n🔄 DUPLICATES DETECTED:');
      console.log('Potential duplicates found. Review duplicate-customers.json before import.');
    }

    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Review generated reports for data quality issues');
    console.log('2. Handle any duplicates identified');
    console.log('3. Import processed-customers.csv into Supabase customers table');
    console.log('4. Verify Square Customer ID integration');
  }
}

// Export for use as module
module.exports = GoogleContactsProcessor;

// Run if called directly
if (require.main === module) {
  const processor = new GoogleContactsProcessor();
  processor.processGoogleContacts().catch(console.error);
}
