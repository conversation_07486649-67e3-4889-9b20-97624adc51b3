/**
 * Square Payment Performance Monitor
 * Monitors and optimizes performance issues specific to Square payment integration
 */

(function() {
  'use strict';

  // Performance thresholds
  const PERFORMANCE_THRESHOLDS = {
    SLOW_TIMEOUT: 100, // ms - threshold for slow setTimeout
    FORCED_REFLOW: 100, // ms - threshold for forced reflow
    IFRAME_LOAD: 3000, // ms - threshold for iframe loading
    SCRIPT_LOAD: 5000, // ms - threshold for script loading
    DOM_OPERATION: 50 // ms - threshold for DOM operations
  };

  // Performance metrics storage
  const performanceMetrics = {
    timeouts: [],
    reflows: [],
    iframeLoads: [],
    scriptLoads: [],
    domOperations: [],
    violations: []
  };

  // Performance violation tracking
  let violationCount = 0;
  let lastViolationTime = 0;

  // Browser-safe environment detection
  function isDevelopmentEnvironment() {
    // Check for development indicators in browser environment
    try {
      // Check if we're on localhost or development domains
      const hostname = window.location.hostname;
      const isDevelopmentHost = hostname === 'localhost' ||
                               hostname === '127.0.0.1' ||
                               hostname.includes('dev') ||
                               hostname.includes('staging') ||
                               hostname.includes('test');

      // Check for development URL patterns
      const isDevelopmentPort = window.location.port &&
                               (window.location.port === '3000' ||
                                window.location.port === '3001' ||
                                window.location.port === '8000' ||
                                window.location.port === '8080');

      // Check for debug flags
      const hasDebugFlag = window.location.search.includes('debug') ||
                          window.location.hash.includes('debug') ||
                          window.localStorage.getItem('debug') === 'true';

      // Check for development console flag
      const hasDevConsole = window.console && window.console.debug;

      return isDevelopmentHost || isDevelopmentPort || hasDebugFlag || hasDevConsole;
    } catch (error) {
      // Fallback: assume production if we can't determine
      return false;
    }
  }

  /**
   * Initialize performance monitoring
   */
  function initializePerformanceMonitoring() {
    // Add browser compatibility check
    if (typeof window === 'undefined') {
      console.warn('Square performance monitor: Not running in browser environment');
      return;
    }

    // Monitor Long Task API for performance violations
    if ('PerformanceObserver' in window) {
      try {
        // Monitor long tasks
        const longTaskObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > PERFORMANCE_THRESHOLDS.SLOW_TIMEOUT) {
              recordPerformanceViolation('long-task', entry.duration, {
                name: entry.name,
                startTime: entry.startTime,
                attribution: entry.attribution
              });
            }
          }
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });

        // Monitor layout shifts and reflows
        const layoutObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.value > 0.1) { // Significant layout shift
              recordPerformanceViolation('layout-shift', entry.value * 1000, {
                sources: entry.sources,
                hadRecentInput: entry.hadRecentInput
              });
            }
          }
        });
        layoutObserver.observe({ entryTypes: ['layout-shift'] });

        // Monitor resource loading
        const resourceObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name.includes('square') || entry.name.includes('iframe')) {
              const loadTime = entry.responseEnd - entry.startTime;
              if (loadTime > PERFORMANCE_THRESHOLDS.SCRIPT_LOAD) {
                recordPerformanceViolation('slow-resource', loadTime, {
                  name: entry.name,
                  type: entry.initiatorType,
                  size: entry.transferSize
                });
              }
            }
          }
        });
        resourceObserver.observe({ entryTypes: ['resource'] });

      } catch (error) {
        console.warn('Performance monitoring setup failed:', error);
      }
    } else {
      console.warn('PerformanceObserver not supported in this browser');
    }

    // Override setTimeout to monitor slow operations
    try {
      monitorSetTimeout();
    } catch (error) {
      console.warn('setTimeout monitoring setup failed:', error);
    }

    // Monitor DOM operations that might cause reflows
    try {
      monitorDOMOperations();
    } catch (error) {
      console.warn('DOM operations monitoring setup failed:', error);
    }

    // Monitor iframe loading specifically for Square
    try {
      monitorSquareIframes();
    } catch (error) {
      console.warn('Square iframe monitoring setup failed:', error);
    }

    console.log('🚀 Square performance monitoring initialized');
  }

  /**
   * Monitor setTimeout operations for performance violations
   */
  function monitorSetTimeout() {
    const originalSetTimeout = window.setTimeout;
    
    window.setTimeout = function(callback, delay, ...args) {
      const startTime = performance.now();
      
      const wrappedCallback = function() {
        const executionStart = performance.now();
        
        try {
          const result = callback.apply(this, args);
          
          const executionTime = performance.now() - executionStart;
          if (executionTime > PERFORMANCE_THRESHOLDS.SLOW_TIMEOUT) {
            recordPerformanceViolation('slow-timeout', executionTime, {
              delay: delay,
              stackTrace: getStackTrace()
            });
          }
          
          return result;
        } catch (error) {
          console.error('Error in setTimeout callback:', error);
          throw error;
        }
      };
      
      return originalSetTimeout.call(this, wrappedCallback, delay, ...args);
    };
  }

  /**
   * Monitor DOM operations that might cause forced reflows
   */
  function monitorDOMOperations() {
    // Monitor common DOM operations that trigger reflows
    const reflowProperties = [
      'offsetTop', 'offsetLeft', 'offsetWidth', 'offsetHeight',
      'scrollTop', 'scrollLeft', 'scrollWidth', 'scrollHeight',
      'clientTop', 'clientLeft', 'clientWidth', 'clientHeight',
      'getComputedStyle'
    ];

    // Override Element prototype methods
    reflowProperties.forEach(prop => {
      if (Element.prototype.hasOwnProperty(prop)) {
        const originalDescriptor = Object.getOwnPropertyDescriptor(Element.prototype, prop);
        if (originalDescriptor && originalDescriptor.get) {
          Object.defineProperty(Element.prototype, prop, {
            get: function() {
              const startTime = performance.now();
              const result = originalDescriptor.get.call(this);
              const executionTime = performance.now() - startTime;
              
              if (executionTime > PERFORMANCE_THRESHOLDS.FORCED_REFLOW) {
                recordPerformanceViolation('forced-reflow', executionTime, {
                  property: prop,
                  element: this.tagName,
                  id: this.id,
                  className: this.className
                });
              }
              
              return result;
            },
            configurable: true
          });
        }
      }
    });

    // Monitor getComputedStyle
    const originalGetComputedStyle = window.getComputedStyle;
    window.getComputedStyle = function(element, pseudoElement) {
      const startTime = performance.now();
      const result = originalGetComputedStyle.call(this, element, pseudoElement);
      const executionTime = performance.now() - startTime;
      
      if (executionTime > PERFORMANCE_THRESHOLDS.FORCED_REFLOW) {
        recordPerformanceViolation('forced-reflow', executionTime, {
          operation: 'getComputedStyle',
          element: element.tagName,
          id: element.id,
          className: element.className
        });
      }
      
      return result;
    };
  }

  /**
   * Monitor Square iframe loading and performance
   */
  function monitorSquareIframes() {
    // Monitor iframe creation
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'IFRAME') {
            const src = node.src || '';
            if (src.includes('square') || src.includes('squarecdn.com')) {
              monitorIframeLoad(node);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Monitor existing iframes
    document.querySelectorAll('iframe').forEach(iframe => {
      const src = iframe.src || '';
      if (src.includes('square') || src.includes('squarecdn.com')) {
        monitorIframeLoad(iframe);
      }
    });
  }

  /**
   * Monitor individual iframe loading
   */
  function monitorIframeLoad(iframe) {
    const startTime = performance.now();
    
    const onLoad = () => {
      const loadTime = performance.now() - startTime;
      if (loadTime > PERFORMANCE_THRESHOLDS.IFRAME_LOAD) {
        recordPerformanceViolation('slow-iframe', loadTime, {
          src: iframe.src,
          id: iframe.id,
          className: iframe.className
        });
      }
      cleanup();
    };

    const onError = () => {
      recordPerformanceViolation('iframe-error', performance.now() - startTime, {
        src: iframe.src,
        id: iframe.id,
        error: 'Failed to load'
      });
      cleanup();
    };

    const cleanup = () => {
      iframe.removeEventListener('load', onLoad);
      iframe.removeEventListener('error', onError);
    };

    iframe.addEventListener('load', onLoad);
    iframe.addEventListener('error', onError);

    // Timeout after 10 seconds
    setTimeout(() => {
      if (iframe.contentDocument === null) {
        recordPerformanceViolation('iframe-timeout', 10000, {
          src: iframe.src,
          id: iframe.id
        });
      }
      cleanup();
    }, 10000);
  }

  /**
   * Record a performance violation
   */
  function recordPerformanceViolation(type, duration, details = {}) {
    const violation = {
      type,
      duration: Math.round(duration),
      timestamp: Date.now(),
      details,
      url: window.location.href
    };

    performanceMetrics.violations.push(violation);
    violationCount++;
    lastViolationTime = Date.now();

    // Log significant violations in development
    if (isDevelopmentEnvironment() && duration > 200) {
      console.warn(`🐌 Performance violation: ${type} took ${Math.round(duration)}ms`, details);
    }

    // Trigger optimization suggestions
    if (violationCount % 5 === 0) {
      suggestOptimizations();
    }
  }

  /**
   * Get stack trace for debugging
   */
  function getStackTrace() {
    try {
      throw new Error();
    } catch (e) {
      return e.stack ? e.stack.split('\n').slice(2, 5).join('\n') : 'Stack trace unavailable';
    }
  }

  /**
   * Suggest performance optimizations
   */
  function suggestOptimizations() {
    const recentViolations = performanceMetrics.violations.filter(
      v => Date.now() - v.timestamp < 30000 // Last 30 seconds
    );

    if (recentViolations.length === 0) return;

    const suggestions = [];
    const violationTypes = recentViolations.reduce((acc, v) => {
      acc[v.type] = (acc[v.type] || 0) + 1;
      return acc;
    }, {});

    if (violationTypes['slow-timeout'] > 2) {
      suggestions.push('Consider debouncing or optimizing setTimeout callbacks');
    }

    if (violationTypes['forced-reflow'] > 2) {
      suggestions.push('Batch DOM reads and writes to avoid forced reflows');
    }

    if (violationTypes['slow-iframe'] > 1) {
      suggestions.push('Consider lazy loading or optimizing iframe content');
    }

    if (suggestions.length > 0 && isDevelopmentEnvironment()) {
      console.group('💡 Performance Optimization Suggestions');
      suggestions.forEach(suggestion => console.log(`• ${suggestion}`));
      console.groupEnd();
    }
  }

  /**
   * Get performance report
   */
  window.getSquarePerformanceReport = function() {
    const report = {
      totalViolations: violationCount,
      lastViolationTime: lastViolationTime,
      violations: performanceMetrics.violations.slice(-20), // Last 20 violations
      summary: {}
    };

    // Summarize violations by type
    performanceMetrics.violations.forEach(v => {
      if (!report.summary[v.type]) {
        report.summary[v.type] = { count: 0, totalDuration: 0, avgDuration: 0 };
      }
      report.summary[v.type].count++;
      report.summary[v.type].totalDuration += v.duration;
    });

    // Calculate averages
    Object.keys(report.summary).forEach(type => {
      const summary = report.summary[type];
      summary.avgDuration = Math.round(summary.totalDuration / summary.count);
    });

    return report;
  };

  /**
   * Clear performance metrics
   */
  window.clearSquarePerformanceMetrics = function() {
    performanceMetrics.violations = [];
    violationCount = 0;
    lastViolationTime = 0;
    console.log('Square performance metrics cleared');
  };

  /**
   * Debug function to test performance monitoring
   */
  window.testSquarePerformanceMonitoring = function() {
    console.log('🧪 Testing Square performance monitoring...');

    // Test environment detection
    const isDev = isDevelopmentEnvironment();
    console.log(`Environment detection: ${isDev ? 'Development' : 'Production'}`);

    // Test performance violation recording
    recordPerformanceViolation('test-violation', 250, {
      test: true,
      timestamp: Date.now()
    });

    // Get and display report
    const report = window.getSquarePerformanceReport();
    console.log('Performance report:', report);

    console.log('✅ Performance monitoring test completed');
    return report;
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePerformanceMonitoring);
  } else {
    initializePerformanceMonitoring();
  }

})();
