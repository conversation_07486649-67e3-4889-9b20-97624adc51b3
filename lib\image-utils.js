/**
 * Image URL utilities for proper path resolution
 * Fixes issues with incorrect /admin/ prefixed URLs and Wix image handling
 */

/**
 * Resolve image URL to proper format
 * @param {string} imageUrl - The raw image URL from database
 * @param {string} fallback - Fallback image URL if resolution fails
 * @returns {string} - Properly resolved image URL
 */
export const resolveImageUrl = (imageUrl, fallback = '/images/placeholder.svg') => {
  try {
    // Handle null, undefined, or empty values
    if (!imageUrl || typeof imageUrl !== 'string') {
      return fallback;
    }

    // Clean the URL string
    const cleanUrl = imageUrl.trim();
    
    // If it's already a full URL (starts with http/https), use as-is
    if (cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')) {
      return cleanUrl;
    }

    // If it starts with /admin/, remove the /admin/ prefix
    if (cleanUrl.startsWith('/admin/')) {
      const correctedPath = cleanUrl.replace('/admin/', '/');
      console.log(`[Image Utils] Corrected admin path: ${cleanUrl} -> ${correctedPath}`);
      return correctedPath;
    }

    // If it's a relative path starting with /, use as-is
    if (cleanUrl.startsWith('/')) {
      return cleanUrl;
    }

    // Handle Wix-style image names (debcf9_[hash]~mv2.jpg)
    if (cleanUrl.match(/^debcf9_[a-f0-9]+~mv2\.(jpg|jpeg|png|webp)$/i)) {
      // These are Wix-generated image names that should be served from root
      const resolvedUrl = `/${cleanUrl}`;
      console.log(`[Image Utils] Resolved Wix image: ${cleanUrl} -> ${resolvedUrl}`);
      return resolvedUrl;
    }

    // Handle multiple image URLs separated by semicolons (fix for concatenated URLs)
    if (cleanUrl.includes(';')) {
      const firstImage = cleanUrl.split(';')[0].trim();
      console.log(`[Image Utils] Multiple URLs detected, using first: ${firstImage}`);
      return resolveImageUrl(firstImage, fallback); // Recursive call for first image
    }

    // If it doesn't start with /, assume it's a filename and prepend /
    if (!cleanUrl.startsWith('/')) {
      const resolvedUrl = `/${cleanUrl}`;
      console.log(`[Image Utils] Added leading slash: ${cleanUrl} -> ${resolvedUrl}`);
      return resolvedUrl;
    }

    // Default case - return the cleaned URL
    return cleanUrl;

  } catch (error) {
    console.error('[Image Utils] Error resolving image URL:', error, 'Original URL:', imageUrl);
    return fallback;
  }
};

/**
 * Resolve multiple image URLs (for galleries, etc.)
 * @param {string|array} imageUrls - Single URL string or array of URLs
 * @param {string} fallback - Fallback image URL
 * @returns {array} - Array of resolved image URLs
 */
export const resolveImageUrls = (imageUrls, fallback = '/images/placeholder.svg') => {
  try {
    if (!imageUrls) {
      return [fallback];
    }

    // If it's already an array, resolve each URL
    if (Array.isArray(imageUrls)) {
      return imageUrls.map(url => resolveImageUrl(url, fallback));
    }

    // If it's a string with multiple URLs (semicolon or comma separated)
    if (typeof imageUrls === 'string') {
      const separators = [';', ','];
      let urls = [imageUrls];
      
      // Split by any of the separators
      for (const separator of separators) {
        if (imageUrls.includes(separator)) {
          urls = imageUrls.split(separator).map(url => url.trim()).filter(Boolean);
          break;
        }
      }

      return urls.map(url => resolveImageUrl(url, fallback));
    }

    // Fallback to single URL resolution
    return [resolveImageUrl(imageUrls, fallback)];

  } catch (error) {
    console.error('[Image Utils] Error resolving multiple image URLs:', error, 'Original URLs:', imageUrls);
    return [fallback];
  }
};

/**
 * Generate optimized image URL with size parameters
 * @param {string} imageUrl - The base image URL
 * @param {object} options - Size and optimization options
 * @returns {string} - Optimized image URL
 */
export const getOptimizedImageUrl = (imageUrl, options = {}) => {
  const {
    width = null,
    height = null,
    quality = 80,
    format = null
  } = options;

  try {
    const resolvedUrl = resolveImageUrl(imageUrl);
    
    // If it's a local image, we can't optimize it server-side
    if (resolvedUrl.startsWith('/')) {
      return resolvedUrl;
    }

    // For external URLs, we could add optimization parameters if supported
    // This is a placeholder for future CDN integration
    return resolvedUrl;

  } catch (error) {
    console.error('[Image Utils] Error generating optimized image URL:', error);
    return resolveImageUrl(imageUrl);
  }
};

/**
 * Check if an image URL is valid and accessible
 * @param {string} imageUrl - The image URL to validate
 * @returns {Promise<boolean>} - True if image is accessible
 */
export const validateImageUrl = async (imageUrl) => {
  try {
    const resolvedUrl = resolveImageUrl(imageUrl);
    
    // For client-side validation, we can use Image object
    if (typeof window !== 'undefined') {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = resolvedUrl;
      });
    }

    // For server-side, we'd need to make a HEAD request
    // This is a simplified version
    return true;

  } catch (error) {
    console.error('[Image Utils] Error validating image URL:', error);
    return false;
  }
};

/**
 * Get image dimensions from URL (client-side only)
 * @param {string} imageUrl - The image URL
 * @returns {Promise<object>} - Object with width and height
 */
export const getImageDimensions = async (imageUrl) => {
  if (typeof window === 'undefined') {
    return { width: 0, height: 0 };
  }

  try {
    const resolvedUrl = resolveImageUrl(imageUrl);
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      img.src = resolvedUrl;
    });

  } catch (error) {
    console.error('[Image Utils] Error getting image dimensions:', error);
    return { width: 0, height: 0 };
  }
};

/**
 * Create a placeholder image URL with specified dimensions and text
 * @param {number} width - Image width
 * @param {number} height - Image height  
 * @param {string} text - Placeholder text
 * @param {string} bgColor - Background color (hex)
 * @param {string} textColor - Text color (hex)
 * @returns {string} - Data URL for placeholder image
 */
export const createPlaceholderImage = (width = 300, height = 200, text = 'No Image', bgColor = '#f0f0f0', textColor = '#666') => {
  try {
    if (typeof window === 'undefined') {
      return '/images/placeholder.svg';
    }

    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    
    const ctx = canvas.getContext('2d');
    
    // Fill background
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, width, height);
    
    // Add text
    ctx.fillStyle = textColor;
    ctx.font = `${Math.min(width, height) / 10}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, width / 2, height / 2);
    
    return canvas.toDataURL();

  } catch (error) {
    console.error('[Image Utils] Error creating placeholder image:', error);
    return '/images/placeholder.svg';
  }
};
