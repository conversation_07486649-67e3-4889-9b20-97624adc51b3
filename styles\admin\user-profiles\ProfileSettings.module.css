.profileSettings {
  padding: 30px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.headerContent h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.headerContent p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.saveButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.saveButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.userSelection {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.userSelection label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  font-size: 1rem;
}

.userSelection select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  transition: border-color 0.2s ease;
}

.userSelection select:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.settingsContainer {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.settingsSection {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.settingsSection h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.4rem;
  font-weight: 600;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
}

.settingsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.settingItem {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.checkboxLabel:hover {
  background: #f8f9ff;
}

.checkboxLabel input[type="checkbox"] {
  position: relative;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.checkboxCustom {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkboxLabel input[type="checkbox"]:checked + .checkboxCustom {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  border-color: #6e8efb;
}

.checkboxLabel input[type="checkbox"]:checked + .checkboxCustom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.selectGroup,
.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selectGroup label,
.inputGroup label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.selectGroup select,
.inputGroup input {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.selectGroup select:focus,
.inputGroup input:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.jsonGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.jsonGroup label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.jsonTextarea {
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.85rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f9fa;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.2s ease;
}

.jsonTextarea:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
  background: white;
}

.settingDescription {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
  font-style: italic;
}

.widgetsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.widgetCard {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.widgetCard:hover {
  background: white;
  border-color: #6e8efb;
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.15);
}

.widgetHeader {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.widgetToggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.widgetToggle input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggleSlider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggleSlider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

.widgetToggle input:checked + .toggleSlider {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
}

.widgetToggle input:checked + .toggleSlider:before {
  transform: translateX(26px);
}

.widgetCard h4 {
  margin: 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.widgetCard p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.emptyState h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.3rem;
}

.emptyState p {
  margin: 0;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profileSettings {
    padding: 20px;
  }
  
  .header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .settingsGrid {
    grid-template-columns: 1fr;
  }
  
  .widgetsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profileSettings {
    padding: 15px;
  }
  
  .settingsSection {
    padding: 20px;
  }
  
  .userSelection {
    padding: 20px;
  }
}
