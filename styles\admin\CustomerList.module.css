.customerList {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.header h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 12px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.exportDropdown {
  position: relative;
}

.exportOptions {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 10;
  min-width: 200px;
  display: none;
}

.exportDropdown:hover .exportOptions {
  display: block;
}

.exportOptions button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: #333;
}

.exportOptions button:hover {
  background-color: #f5f5f5;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.searchContainer {
  position: relative;
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  border-color: #6e8efb;
  outline: none;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.filterControls {
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  align-items: center;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.filterSelect:focus {
  border-color: #6e8efb;
  outline: none;
}

.tableContainer {
  overflow: auto;
  flex: 1;
  min-height: 0;
}

.customerTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.customerTable th {
  background-color: #f9f9f9;
  padding: 10px 12px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #eaeaea;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: sticky;
  top: 0;
  z-index: 1;
}

.customerTable th:hover {
  background-color: #f0f0f0;
}

.customerTable td {
  padding: 10px 12px;
  border-bottom: 1px solid #eaeaea;
  color: #333;
}

.customerTable tr:hover {
  background-color: rgba(110, 142, 251, 0.05);
}

.sortIndicator {
  display: inline-block;
  margin-left: 4px;
  color: #6e8efb;
}

.actions {
  display: flex;
  gap: 8px;
}

.viewButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: #6e8efb;
  color: white;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.editButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.marketingBadge {
  display: inline-block;
  margin-left: 8px;
  font-size: 0.8rem;
  color: #6e8efb;
}

.newBadge {
  display: inline-block;
  margin-left: 8px;
  background-color: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.recentBooking {
  background-color: rgba(255, 235, 59, 0.1);
}

/* Statistics Cards */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #6c757d;
}

.statIcon.active {
  background-color: #d4edda;
  color: #155724;
}

.statIcon.new {
  background-color: #cce5ff;
  color: #0066cc;
}

.statIcon.bookings {
  background-color: #fff3cd;
  color: #856404;
}

.statContent {
  flex: 1;
}

.statNumber {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  margin-top: 4px;
}

/* Bulk Actions */
.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #6e8efb;
}

.selectedCount {
  font-weight: 500;
  color: #333;
}

.bulkActionButtons {
  position: relative;
}

.bulkButton {
  padding: 8px 16px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.bulkActionMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
}

.bulkActionMenu button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bulkActionMenu button:hover {
  background: #f5f5f5;
}

.bulkActionMenu button:first-child {
  border-radius: 6px 6px 0 0;
}

.bulkActionMenu button:last-child {
  border-radius: 0 0 6px 6px;
}

/* Checkbox column */
.checkboxColumn {
  width: 40px;
  cursor: default !important;
}

.checkboxColumn:hover {
  background-color: #f9f9f9 !important;
}

/* Customer name styling */
.customerName {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Status and tier badges */
.statusBadge {
  display: inline-block;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.tierBadge {
  display: inline-block;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.noResults {
  text-align: center;
  padding: 32px;
  color: #666;
  font-size: 1.1rem;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding: 12px 0;
  border-top: 1px solid #eaeaea;
  flex-shrink: 0;
}

.pageSize {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #666;
}

.pageSizeSelect {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.paginationButton {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  color: #666;
  font-size: 0.9rem;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .customerTable th:nth-child(5),
  .customerTable td:nth-child(5) {
    display: none;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
  }

  .header h2 {
    font-size: 1.3rem;
  }

  .actions {
    width: 100%;
    justify-content: flex-start;
    gap: 8px;
  }

  .addButton,
  .exportButton {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .filters {
    gap: 8px;
    margin-bottom: 12px;
  }

  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .filterControls {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filterSelect {
    padding: 6px 10px;
    font-size: 0.85rem;
  }

  .searchInput {
    padding: 8px 12px 8px 36px;
    font-size: 0.9rem;
  }

  .bulkActions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    padding: 12px 16px;
  }

  .customerTable {
    font-size: 0.85rem;
  }

  .customerTable th,
  .customerTable td {
    padding: 8px 10px;
  }

  .customerTable th:nth-child(6),
  .customerTable td:nth-child(6) {
    display: none;
  }

  .paginationContainer {
    flex-direction: column;
    gap: 12px;
    align-items: center;
    margin-top: 12px;
    padding: 10px 0;
  }

  .pagination {
    gap: 8px;
  }

  .paginationButton {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .statCard {
    padding: 15px;
  }

  .statIcon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .customerTable th:nth-child(7),
  .customerTable td:nth-child(7) {
    display: none;
  }

  .customerName {
    font-size: 0.9rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .viewButton,
  .editButton {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
}
