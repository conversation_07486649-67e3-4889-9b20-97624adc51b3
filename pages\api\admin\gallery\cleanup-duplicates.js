/**
 * Cleanup Duplicate Gallery Items
 * Removes duplicate gallery items, keeping only the most recent version of each
 */

import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-auth-token');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error?.message || 'Unauthorized' });
    }

    const supabaseAdmin = getAdminClient();

    // Find all duplicate items (items with same title)
    const { data: duplicates, error: duplicatesError } = await supabaseAdmin
      .from('gallery_items')
      .select('title, id, created_at')
      .order('title')
      .order('created_at', { ascending: false });

    if (duplicatesError) {
      console.error('Error fetching duplicates:', duplicatesError);
      return res.status(500).json({ error: 'Failed to fetch duplicates' });
    }

    // Group by title and identify duplicates to remove
    const titleGroups = {};
    duplicates.forEach(item => {
      if (!titleGroups[item.title]) {
        titleGroups[item.title] = [];
      }
      titleGroups[item.title].push(item);
    });

    const itemsToDelete = [];
    let duplicatesFound = 0;

    Object.entries(titleGroups).forEach(([title, items]) => {
      if (items.length > 1) {
        duplicatesFound++;
        // Keep the most recent (first in the sorted array), delete the rest
        const [keep, ...toDelete] = items;
        itemsToDelete.push(...toDelete.map(item => item.id));
        console.log(`Title "${title}": Keeping ${keep.id}, deleting ${toDelete.length} duplicates`);
      }
    });

    if (itemsToDelete.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No duplicates found',
        duplicatesFound: 0,
        itemsDeleted: 0
      });
    }

    // Delete duplicate items
    const { error: deleteError } = await supabaseAdmin
      .from('gallery_items')
      .delete()
      .in('id', itemsToDelete);

    if (deleteError) {
      console.error('Error deleting duplicates:', deleteError);
      return res.status(500).json({ error: 'Failed to delete duplicates' });
    }

    // Get final count
    const { count: finalCount, error: countError } = await supabaseAdmin
      .from('gallery_items')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error getting final count:', countError);
    }

    return res.status(200).json({
      success: true,
      message: 'Duplicates cleaned up successfully',
      duplicatesFound,
      itemsDeleted: itemsToDelete.length,
      finalCount: finalCount || 'unknown'
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
