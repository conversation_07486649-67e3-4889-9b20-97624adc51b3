<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Admin Edit Reflection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .product-category {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .product-description {
            color: #555;
            font-size: 0.9em;
            line-height: 1.4;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Admin Products Data Flow Test</h1>
        <p>This test verifies that admin edits to products immediately reflect on the public shop page.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testAdminAPI()">Test Admin API</button>
            <button onclick="testPublicAPI()">Test Public API</button>
            <button onclick="testDataFlow()">Test Complete Data Flow</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click a test button to start testing...</div>
            </div>
        </div>

        <div class="test-section">
            <h3>Admin API Products</h3>
            <div id="admin-products" class="loading">Not loaded yet</div>
        </div>

        <div class="test-section">
            <h3>Public API Products</h3>
            <div id="public-products" class="loading">Not loaded yet</div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
            document.getElementById('admin-products').innerHTML = '<div class="loading">Not loaded yet</div>';
            document.getElementById('public-products').innerHTML = '<div class="loading">Not loaded yet</div>';
        }

        async function testAdminAPI() {
            addResult('Testing Admin API...', 'info');
            
            try {
                const response = await fetch('/api/admin/inventory/products');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const products = data.products || [];
                
                addResult(`✅ Admin API: Found ${products.length} products`, 'success');
                
                // Display products
                displayProducts(products, 'admin-products');
                
                // Check for issues
                const missingImages = products.filter(p => !p.image_url || !p.image_url.startsWith('/images/products/'));
                const missingCategories = products.filter(p => !p.category);
                
                if (missingImages.length > 0) {
                    addResult(`⚠️ ${missingImages.length} products missing proper images`, 'warning');
                }
                
                if (missingCategories.length > 0) {
                    addResult(`⚠️ ${missingCategories.length} products missing categories`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Admin API Error: ${error.message}`, 'error');
            }
        }

        async function testPublicAPI() {
            addResult('Testing Public API...', 'info');
            
            try {
                const response = await fetch('/api/public/products');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const products = data.products || [];
                
                addResult(`✅ Public API: Found ${products.length} products`, 'success');
                
                // Display products
                displayProducts(products, 'public-products');
                
                // Check for issues
                const missingImages = products.filter(p => !p.image || p.image === '/images/placeholder.svg');
                const missingDescriptions = products.filter(p => !p.description || p.description.length < 50);
                
                if (missingImages.length > 0) {
                    addResult(`⚠️ ${missingImages.length} products using placeholder images`, 'warning');
                }
                
                if (missingDescriptions.length > 0) {
                    addResult(`⚠️ ${missingDescriptions.length} products with short descriptions`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Public API Error: ${error.message}`, 'error');
            }
        }

        async function testDataFlow() {
            addResult('Testing complete data flow...', 'info');
            
            await testAdminAPI();
            await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
            await testPublicAPI();
            
            addResult('✅ Data flow test completed', 'success');
        }

        function displayProducts(products, containerId) {
            const container = document.getElementById(containerId);
            
            if (products.length === 0) {
                container.innerHTML = '<div class="warning status">No products found</div>';
                return;
            }
            
            const productCards = products.slice(0, 6).map(product => {
                const imageUrl = product.image_url || product.image || '/images/placeholder.svg';
                const name = product.name || 'Unnamed Product';
                const category = product.category || product.category_name || 'No Category';
                const description = product.description || 'No description available';
                
                return `
                    <div class="product-card">
                        <img src="${imageUrl}" alt="${name}" class="product-image" 
                             onerror="this.src='/images/placeholder.svg'">
                        <div class="product-name">${name}</div>
                        <div class="product-category">Category: ${category}</div>
                        <div class="product-description">${description.substring(0, 100)}${description.length > 100 ? '...' : ''}</div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = `
                <div class="product-grid">
                    ${productCards}
                </div>
                ${products.length > 6 ? `<p><em>Showing first 6 of ${products.length} products</em></p>` : ''}
            `;
        }

        // Auto-run initial test
        window.addEventListener('load', () => {
            setTimeout(testDataFlow, 1000);
        });
    </script>
</body>
</html>
