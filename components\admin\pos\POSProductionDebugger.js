/**
 * POS Production Debugger
 *
 * This component provides real-time debugging specifically for POS terminal issues
 * in production mode, helping identify container initialization and auth recovery problems.
 */

import { useEffect, useState } from 'react';

const POSProductionDebugger = () => {
  const [debugInfo, setDebugInfo] = useState({});
  const [isVisible, setIsVisible] = useState(false);
  const [containerRetries, setContainerRetries] = useState(0);
  const [authRecoveryAttempts, setAuthRecoveryAttempts] = useState(0);

  // Monitor POS-specific issues
  useEffect(() => {
    const collectPOSDebugInfo = () => {
      const info = {
        timestamp: new Date().toISOString(),

        // Enhanced container status with multiple detection strategies
        container: {
          // Strategy 1: Direct DOM query
          domQuery: !!document.getElementById('pos-square-card-container'),

          // Strategy 2: Test ID query
          testIdQuery: !!document.querySelector('[data-testid="square-card-container"]'),

          // Strategy 3: Class-based queries
          hasSquareClass: !!document.querySelector('[class*="square"]'),
          hasPOSClass: !!document.querySelector('[class*="pos"]'),
          hasCardFormClass: !!document.querySelector('.cardForm'),

          // Container dimensions and visibility
          containerDimensions: (() => {
            const el = document.getElementById('pos-square-card-container');
            if (el) {
              const rect = el.getBoundingClientRect();
              return {
                width: rect.width,
                height: rect.height,
                visible: rect.width > 0 && rect.height > 0,
                top: rect.top,
                left: rect.left
              };
            }
            return null;
          })(),

          // Parent container status
          parentContainers: {
            cardFormContainer: !!document.querySelector('.cardFormContainer'),
            squarePaymentContainer: !!document.querySelector('.squarePaymentContainer'),
            bodyChildren: document.body.children.length
          }
        },

        // Square SDK status
        square: {
          sdkLoaded: !!window.Square,
          sdkVersion: window.Square?.version || 'unknown',
          paymentsAvailable: !!(window.Square?.payments)
        },

        // POS protection status
        protection: {
          posOperationActive: sessionStorage.getItem('pos_operation_active') === 'true',
          posPaymentInProgress: sessionStorage.getItem('pos_payment_in_progress') === 'true',
          posSessionProtected: !!sessionStorage.getItem('pos_session_protected'),
          authRedirecting: sessionStorage.getItem('auth_redirecting') === 'true'
        },

        // Auth recovery status
        authRecovery: {
          recoverySystemLoaded: !!window.authWhiteScreenRecovery,
          isStuck: window.authWhiteScreenRecovery?.isStuck?.() || false,
          lastRecoveryAttempt: localStorage.getItem('last_auth_recovery_attempt')
        },

        // React hydration status
        hydration: {
          documentReady: document.readyState,
          bodyChildren: document.body.children.length,
          hasReactRoot: !!document.querySelector('#__next'),
          reactDevTools: !!(window.__REACT_DEVTOOLS_GLOBAL_HOOK__)
        },

        // Performance metrics
        performance: {
          navigationStart: performance.timing?.navigationStart,
          domContentLoaded: performance.timing?.domContentLoaded,
          loadComplete: performance.timing?.loadEventEnd,
          memoryUsage: performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
          } : null
        }
      };

      setDebugInfo(info);
    };

    collectPOSDebugInfo();

    // Update every 2 seconds
    const interval = setInterval(collectPOSDebugInfo, 2000);

    return () => clearInterval(interval);
  }, []);

  // Monitor console for container retry messages
  useEffect(() => {
    const originalConsoleLog = console.log;

    console.log = (...args) => {
      originalConsoleLog.apply(console, args);

      const message = args.join(' ');
      if (message.includes('Container ref not ready, retrying')) {
        setContainerRetries(prev => prev + 1);
        setIsVisible(true);
      }

      if (message.includes('[Auth Recovery]')) {
        setAuthRecoveryAttempts(prev => prev + 1);
        setIsVisible(true);
      }
    };

    return () => {
      console.log = originalConsoleLog;
    };
  }, []);

  // Auto-show debugger when issues are detected
  useEffect(() => {
    if (containerRetries > 10 || authRecoveryAttempts > 0 ||
        debugInfo.authRecovery?.isStuck ||
        !debugInfo.container?.refExists) {
      setIsVisible(true);
    }
  }, [containerRetries, authRecoveryAttempts, debugInfo]);

  // Force container initialization
  const forceContainerInit = () => {
    console.log('🔧 Forcing container initialization...');

    // Dispatch custom event to force Square initialization
    const event = new CustomEvent('forceSquareInit', {
      detail: { source: 'POSProductionDebugger' }
    });
    window.dispatchEvent(event);
  };

  // Clear all POS protection flags
  const clearPOSProtection = () => {
    console.log('🧹 Clearing POS protection flags...');

    sessionStorage.removeItem('pos_operation_active');
    sessionStorage.removeItem('pos_payment_in_progress');
    sessionStorage.removeItem('pos_session_protected');
    sessionStorage.removeItem('auth_redirecting');

    setDebugInfo(prev => ({
      ...prev,
      protection: {
        posOperationActive: false,
        posPaymentInProgress: false,
        posSessionProtected: false,
        authRedirecting: false
      }
    }));
  };

  // Reset counters
  const resetCounters = () => {
    setContainerRetries(0);
    setAuthRecoveryAttempts(0);
  };

  // Test auth recovery protection
  const testAuthRecoveryProtection = () => {
    console.log('🧪 Testing auth recovery protection...');

    if (window.authWhiteScreenRecovery) {
      const isStuck = window.authWhiteScreenRecovery.isStuck();
      console.log('Auth recovery stuck check result:', isStuck);

      if (isStuck) {
        console.warn('⚠️ Auth recovery thinks we are stuck!');
      } else {
        console.log('✅ Auth recovery protection is working');
      }
    } else {
      console.warn('Auth recovery system not loaded');
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: 'fixed',
          bottom: '60px',
          right: '10px',
          backgroundColor: containerRetries > 10 || authRecoveryAttempts > 0 ? '#ff4444' : '#333',
          color: '#fff',
          border: 'none',
          borderRadius: '4px',
          padding: '8px 12px',
          fontSize: '12px',
          cursor: 'pointer',
          zIndex: 9999
        }}
      >
        🏪 POS Debug ({containerRetries > 0 ? `${containerRetries} retries` : 'OK'})
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '10px',
      right: '10px',
      width: '400px',
      maxHeight: '500px',
      backgroundColor: '#1a1a1a',
      color: '#fff',
      border: '1px solid #444',
      borderRadius: '4px',
      padding: '15px',
      fontSize: '11px',
      fontFamily: 'monospace',
      zIndex: 10000,
      overflow: 'auto',
      boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '15px',
        borderBottom: '1px solid #444',
        paddingBottom: '10px'
      }}>
        <strong style={{ color: '#4CAF50' }}>🏪 POS Production Debugger</strong>
        <button
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          ×
        </button>
      </div>

      {/* Status Overview */}
      <div style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
        <div style={{ color: containerRetries > 10 ? '#ff6666' : '#4CAF50', marginBottom: '5px' }}>
          <strong>Container Retries: {containerRetries}</strong>
          {containerRetries > 10 && ' ⚠️ EXCESSIVE'}
        </div>
        <div style={{ color: authRecoveryAttempts > 0 ? '#ff6666' : '#4CAF50', marginBottom: '5px' }}>
          <strong>Auth Recovery Attempts: {authRecoveryAttempts}</strong>
          {authRecoveryAttempts > 0 && ' ⚠️ TRIGGERED'}
        </div>
        <div style={{ color: debugInfo.container?.refExists ? '#4CAF50' : '#ff6666' }}>
          <strong>Container Status: {debugInfo.container?.refExists ? 'EXISTS' : 'MISSING'}</strong>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ marginBottom: '15px' }}>
        <button
          onClick={forceContainerInit}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🔧 Force Init
        </button>
        <button
          onClick={clearPOSProtection}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🧹 Clear Protection
        </button>
        <button
          onClick={testAuthRecoveryProtection}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginRight: '5px',
            marginBottom: '5px'
          }}
        >
          🧪 Test Protection
        </button>
        <button
          onClick={resetCounters}
          style={{
            background: '#333',
            border: '1px solid #555',
            color: '#fff',
            padding: '5px 10px',
            borderRadius: '3px',
            cursor: 'pointer',
            fontSize: '11px',
            marginBottom: '5px'
          }}
        >
          🔄 Reset
        </button>
      </div>

      {/* Debug Information */}
      <div style={{ fontSize: '10px' }}>
        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Container Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.container, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Protection Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.protection, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Square SDK Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.square, null, 2)}
          </div>
        </details>

        <details style={{ marginBottom: '10px' }}>
          <summary style={{ cursor: 'pointer', color: '#4CAF50' }}>Auth Recovery Status</summary>
          <div style={{ marginLeft: '15px', marginTop: '5px' }}>
            {JSON.stringify(debugInfo.authRecovery, null, 2)}
          </div>
        </details>
      </div>

      <div style={{
        marginTop: '15px',
        paddingTop: '10px',
        borderTop: '1px solid #444',
        fontSize: '9px',
        color: '#888'
      }}>
        Last updated: {new Date(debugInfo.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

export default POSProductionDebugger;
