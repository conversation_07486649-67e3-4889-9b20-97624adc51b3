import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/router'

export default function AuthDebug() {
  const { user, role, loading, error, isAuthenticated } = useAuth()
  const router = useRouter()
  const [logs, setLogs] = useState([])
  const [startTime] = useState(Date.now())

  const addLog = (message) => {
    const timestamp = Date.now() - startTime
    const logEntry = `[${timestamp}ms] ${message}`
    console.log(logEntry)
    setLogs(prev => [...prev, logEntry])
  }

  useEffect(() => {
    addLog('AuthDebug component mounted')
    
    // Log auth state changes
    addLog(`Auth state: loading=${loading}, user=${!!user}, role=${role}, error=${!!error}`)
  }, [loading, user, role, error])

  useEffect(() => {
    // Add browser console error capture
    const originalError = console.error
    const originalLog = console.log
    
    console.error = (...args) => {
      addLog(`ERROR: ${args.join(' ')}`)
      originalError.apply(console, args)
    }
    
    console.log = (...args) => {
      if (args[0] && typeof args[0] === 'string' && args[0].includes('Auth')) {
        addLog(`LOG: ${args.join(' ')}`)
      }
      originalLog.apply(console, args)
    }

    return () => {
      console.error = originalError
      console.log = originalLog
    }
  }, [])

  const clearLogs = () => {
    setLogs([])
  }

  const testRedirect = () => {
    addLog('Testing redirect to /admin')
    router.push('/admin')
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Authentication Debug</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Auth State */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Auth State</h2>
            <div className="space-y-2">
              <div>Loading: <span className={loading ? 'text-orange-500' : 'text-green-500'}>{loading.toString()}</span></div>
              <div>User: <span className={user ? 'text-green-500' : 'text-red-500'}>{user ? user.email : 'null'}</span></div>
              <div>Role: <span className={role ? 'text-blue-500' : 'text-gray-500'}>{role || 'null'}</span></div>
              <div>Authenticated: <span className={isAuthenticated ? 'text-green-500' : 'text-red-500'}>{isAuthenticated.toString()}</span></div>
              <div>Error: <span className={error ? 'text-red-500' : 'text-green-500'}>{error ? error.message : 'null'}</span></div>
            </div>
          </div>

          {/* Actions */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Actions</h2>
            <div className="space-y-2">
              <button 
                onClick={testRedirect}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Test Redirect to /admin
              </button>
              <button 
                onClick={clearLogs}
                className="w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                Clear Logs
              </button>
            </div>
          </div>
        </div>

        {/* Logs */}
        <div className="mt-6 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
          <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet...</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">{log}</div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
