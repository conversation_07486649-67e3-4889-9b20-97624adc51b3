import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import GalleryList from '@/components/admin/gallery/GalleryList';
import GalleryForm from '@/components/admin/gallery/GalleryForm';
import CategoryManager from '@/components/admin/gallery/CategoryManager';
import GalleryDashboard from '@/components/admin/gallery/GalleryDashboard';
import Modal from '@/components/admin/Modal';
import { authenticatedFetch } from '@/lib/auth-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/InventoryPage.module.css'; // Reuse existing styles

export default function GalleryAdmin() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [selectedItem, setSelectedItem] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [loading, setLoading] = useState(false);

  // Handle tab changes from URL
  useEffect(() => {
    const { tab } = router.query;
    if (tab && ['dashboard', 'gallery', 'categories'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [router.query]);

  // Update URL when tab changes
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    router.push(`/admin/gallery?tab=${tab}`, undefined, { shallow: true });
  };

  // Handle adding new items
  const handleAddItem = (type) => {
    setSelectedItem(null);
    setModalType(type);
    setShowModal(true);
  };

  // Handle editing items
  const handleEditItem = (item, type) => {
    setSelectedItem(item);
    setModalType(type);
    setShowModal(true);
  };

  // Handle saving items (create/update)
  const handleSaveItem = async (itemData) => {
    try {
      setLoading(true);
      const isEditing = selectedItem && selectedItem.id;

      let url, method;
      if (modalType === 'category') {
        url = isEditing
          ? `/api/admin/gallery/categories?id=${selectedItem.id}`
          : '/api/admin/gallery/categories';
        method = isEditing ? 'PUT' : 'POST';
      } else {
        url = isEditing
          ? `/api/admin/gallery?id=${selectedItem.id}`
          : '/api/admin/gallery';
        method = isEditing ? 'PUT' : 'POST';
      }

      const result = await authenticatedFetch(url, {
        method,
        body: JSON.stringify(itemData),
      }, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });

      toast.success(result.message || `${modalType} ${isEditing ? 'updated' : 'created'} successfully`);
      setShowModal(false);
      setSelectedItem(null);
      setModalType(null);
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Error saving item:', error);
      toast.error(error.message || 'Failed to save item');
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting items
  const handleDeleteItem = async (item, type) => {
    if (!confirm(`Are you sure you want to delete this ${type}?`)) {
      return;
    }

    try {
      setLoading(true);

      const url = type === 'category'
        ? `/api/admin/gallery/categories?id=${item.id}`
        : `/api/admin/gallery?id=${item.id}`;

      const result = await authenticatedFetch(url, {
        method: 'DELETE',
      }, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });
      toast.success(result.message || `${type} deleted successfully`);
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Error deleting item:', error);
      toast.error(error.message || 'Failed to delete item');
    } finally {
      setLoading(false);
    }
  };

  // Handle closing modal
  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedItem(null);
    setModalType(null);
  };

  // Handle data migration
  const handleMigrateData = async () => {
    if (!confirm('This will migrate existing gallery data to the database. Continue?')) {
      return;
    }

    try {
      setLoading(true);

      const result = await authenticatedFetch('/api/admin/gallery/migrate', {
        method: 'POST',
      }, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });
      toast.success(result.message || 'Data migration completed successfully');
      setRefreshKey(prev => prev + 1);
    } catch (error) {
      console.error('Error migrating data:', error);
      toast.error(error.message || 'Migration failed');
    } finally {
      setLoading(false);
    }
  };

  // Render modal content based on type
  const renderModalContent = () => {
    switch (modalType) {
      case 'gallery-item':
        return (
          <div className={styles.modalContent}>
            <GalleryForm
              item={selectedItem}
              onSave={handleSaveItem}
              onCancel={handleCloseModal}
              loading={loading}
            />
          </div>
        );
      case 'category':
        return (
          <div className={styles.modalContent}>
            <CategoryManager
              category={selectedItem}
              onSave={handleSaveItem}
              onCancel={handleCloseModal}
              loading={loading}
              isModal={true}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <ProtectedRoute>
      <AdminLayout title="Gallery Management">
        <div className={styles.galleryPage}>
          <div className={styles.header}>
            <h1>Gallery Management</h1>
            <div className={styles.actionButtons}>
              {activeTab === 'gallery' && (
                <button
                  className={styles.addButton}
                  onClick={() => handleAddItem('gallery-item')}
                  disabled={loading}
                >
                  Add Gallery Item
                </button>
              )}
              {activeTab === 'categories' && (
                <button
                  className={styles.addButton}
                  onClick={() => handleAddItem('category')}
                  disabled={loading}
                >
                  Add Category
                </button>
              )}
              <button
                className={styles.migrateButton}
                onClick={handleMigrateData}
                disabled={loading}
              >
                {loading ? 'Migrating...' : 'Migrate Data'}
              </button>
            </div>
          </div>

          <div className={styles.tabContainer}>
            <div className={styles.tabNav}>
              <button
                className={`${styles.tabButton} ${activeTab === 'dashboard' ? styles.active : ''}`}
                onClick={() => handleTabChange('dashboard')}
              >
                Dashboard
              </button>
              <button
                className={`${styles.tabButton} ${activeTab === 'gallery' ? styles.active : ''}`}
                onClick={() => handleTabChange('gallery')}
              >
                Gallery Items
              </button>
              <button
                className={`${styles.tabButton} ${activeTab === 'categories' ? styles.active : ''}`}
                onClick={() => handleTabChange('categories')}
              >
                Categories
              </button>
            </div>

            <div className={styles.tabContent}>
              {activeTab === 'dashboard' && (
                <GalleryDashboard
                  refreshKey={refreshKey}
                  onMigrateData={handleMigrateData}
                />
              )}
              {activeTab === 'gallery' && (
                <GalleryList
                  refreshKey={refreshKey}
                  onEditItem={(item) => handleEditItem(item, 'gallery-item')}
                  onDeleteItem={(item) => handleDeleteItem(item, 'gallery-item')}
                />
              )}
              {activeTab === 'categories' && (
                <CategoryManager
                  refreshKey={refreshKey}
                  onEditCategory={(category) => handleEditItem(category, 'category')}
                  onDeleteCategory={(category) => handleDeleteItem(category, 'category')}
                />
              )}
            </div>
          </div>

          {showModal && (
            <Modal onClose={handleCloseModal}>
              {renderModalContent()}
            </Modal>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
