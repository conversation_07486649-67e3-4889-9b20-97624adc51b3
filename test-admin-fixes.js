/**
 * Test script to verify React Error #130 fixes and data synchronization
 * Run this in the browser console on the admin inventory page
 */

console.log('🧪 Starting Admin Fixes Test Suite...');

// Test 1: Check if ServiceForm can be opened without React Error #130
async function testServiceFormOpening() {
  console.log('📝 Test 1: ServiceForm Opening');
  
  try {
    // Find the first Edit button in the services table
    const editButtons = document.querySelectorAll('button:contains("Edit")');
    if (editButtons.length === 0) {
      console.log('❌ No Edit buttons found. Make sure you are on the Services tab.');
      return false;
    }
    
    console.log(`✅ Found ${editButtons.length} Edit buttons`);
    
    // Click the first edit button
    const firstEditButton = editButtons[0];
    firstEditButton.click();
    
    // Wait for modal to appear
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if ServiceForm modal appeared without errors
    const modal = document.querySelector('[class*="modal"]');
    const serviceForm = document.querySelector('[class*="serviceForm"]');
    
    if (modal && serviceForm) {
      console.log('✅ ServiceForm modal opened successfully');
      
      // Check for any error messages in the form
      const errorElements = serviceForm.querySelectorAll('[class*="error"]');
      if (errorElements.length === 0) {
        console.log('✅ No error messages found in ServiceForm');
        return true;
      } else {
        console.log('❌ Error messages found:', errorElements);
        return false;
      }
    } else {
      console.log('❌ ServiceForm modal did not appear');
      return false;
    }
  } catch (error) {
    console.error('❌ Error during ServiceForm test:', error);
    return false;
  }
}

// Test 2: Check data synchronization by comparing admin and public API responses
async function testDataSynchronization() {
  console.log('📝 Test 2: Data Synchronization');
  
  try {
    // Fetch services from admin API
    const adminResponse = await fetch('/api/admin/services');
    const adminData = await adminResponse.json();
    
    // Fetch services from public API
    const publicResponse = await fetch('/api/public/services');
    const publicData = await publicResponse.json();
    
    console.log('📊 Admin API returned:', adminData.length || 0, 'services');
    console.log('📊 Public API returned:', publicData.services?.length || 0, 'services');
    
    if (adminData.length > 0 && publicData.services?.length > 0) {
      // Compare first service data structure
      const adminService = adminData[0];
      const publicService = publicData.services[0];
      
      console.log('🔍 Admin service sample:', {
        id: adminService.id,
        name: adminService.name,
        price: adminService.price,
        status: adminService.status
      });
      
      console.log('🔍 Public service sample:', {
        id: publicService.id,
        title: publicService.title,
        pricing: publicService.pricing,
        featured: publicService.featured
      });
      
      console.log('✅ Data synchronization test completed');
      return true;
    } else {
      console.log('❌ No services found in one or both APIs');
      return false;
    }
  } catch (error) {
    console.error('❌ Error during data synchronization test:', error);
    return false;
  }
}

// Test 3: Check for React Error #130 in browser console
function testForReactErrors() {
  console.log('📝 Test 3: React Error #130 Detection');
  
  // Store original console.error
  const originalError = console.error;
  let reactErrors = [];
  
  // Override console.error to capture React errors
  console.error = function(...args) {
    const message = args.join(' ');
    if (message.includes('Element type is invalid') || 
        message.includes('Objects are not valid as a React child') ||
        message.includes('React Error #130')) {
      reactErrors.push(message);
    }
    originalError.apply(console, args);
  };
  
  // Restore original console.error after 5 seconds
  setTimeout(() => {
    console.error = originalError;
    
    if (reactErrors.length === 0) {
      console.log('✅ No React Error #130 detected');
    } else {
      console.log('❌ React Error #130 detected:', reactErrors);
    }
  }, 5000);
  
  console.log('🔍 Monitoring for React errors for 5 seconds...');
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running all tests...');
  
  // Start React error monitoring
  testForReactErrors();
  
  // Test ServiceForm opening
  const test1Result = await testServiceFormOpening();
  
  // Test data synchronization
  const test2Result = await testDataSynchronization();
  
  // Summary
  console.log('\n📋 Test Results Summary:');
  console.log('ServiceForm Opening:', test1Result ? '✅ PASS' : '❌ FAIL');
  console.log('Data Synchronization:', test2Result ? '✅ PASS' : '❌ FAIL');
  console.log('React Error Monitoring: In progress...');
  
  if (test1Result && test2Result) {
    console.log('🎉 All tests passed! React Error #130 appears to be fixed.');
  } else {
    console.log('⚠️ Some tests failed. Please check the issues above.');
  }
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
  runAllTests();
}

// Export for manual testing
window.adminFixesTest = {
  runAllTests,
  testServiceFormOpening,
  testDataSynchronization,
  testForReactErrors
};

console.log('💡 Tests are running automatically. You can also run individual tests manually:');
console.log('- window.adminFixesTest.testServiceFormOpening()');
console.log('- window.adminFixesTest.testDataSynchronization()');
console.log('- window.adminFixesTest.testForReactErrors()');
