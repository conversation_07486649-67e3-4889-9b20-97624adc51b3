import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import userManagement from '@/lib/user-management'
import styles from '@/styles/admin/users/UserManagementDashboard.module.css'

export default function UserManagementDashboard() {
  const { hasAdminAccess } = useAuth()
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    newThisMonth: 0,
    pendingApplications: 0,
    roleBreakdown: {
      dev: 0,
      admin: 0,
      artist: 0,
      braider: 0,
      user: 0
    }
  })
  const [recentActivity, setRecentActivity] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [retryCount, setRetryCount] = useState(0)
  const fetchAttemptRef = useRef(false)
  const maxRetries = 3

  useEffect(() => {
    // Prevent multiple simultaneous fetch attempts
    if (hasAdminAccess && !fetchAttemptRef.current && retryCount < maxRetries) {
      fetchDashboardData()
    }
  }, [hasAdminAccess, retryCount])

  const fetchDashboardData = async () => {
    // Prevent concurrent fetch attempts
    if (fetchAttemptRef.current) {
      console.log('UserManagementDashboard: Fetch already in progress, skipping')
      return
    }

    try {
      fetchAttemptRef.current = true
      setLoading(true)
      setError(null)

      console.log('UserManagementDashboard: Fetching dashboard data...')

      // Use fallback data if API fails
      try {
        // Fetch user statistics
        const statsResponse = await userManagement.getUserStats()
        setStats(statsResponse)
        console.log('UserManagementDashboard: Stats fetched successfully')
      } catch (statsError) {
        console.warn('UserManagementDashboard: Stats API failed, using fallback data:', statsError.message)
        // Use fallback stats data
        setStats({
          totalUsers: 0,
          activeUsers: 0,
          newThisMonth: 0,
          pendingApplications: 0,
          roleBreakdown: {
            dev: 0,
            admin: 0,
            artist: 0,
            braider: 0,
            user: 0
          }
        })
      }

      try {
        // Fetch recent activity
        const activityResponse = await userManagement.getRecentActivity()
        setRecentActivity(activityResponse)
        console.log('UserManagementDashboard: Activity fetched successfully')
      } catch (activityError) {
        console.warn('UserManagementDashboard: Activity API failed, using fallback data:', activityError.message)
        // Use fallback activity data
        setRecentActivity([])
      }

      // Reset retry count on success
      setRetryCount(0)

    } catch (err) {
      console.error('UserManagementDashboard: Error fetching dashboard data:', err)
      setError(err.message || 'Failed to load dashboard data')

      // Increment retry count but don't retry automatically to prevent infinite loops
      setRetryCount(prev => prev + 1)
    } finally {
      setLoading(false)
      fetchAttemptRef.current = false
    }
  }

  const getRoleColor = (role) => {
    const colors = {
      dev: '#ff6b6b',
      admin: '#4ecdc4',
      artist: '#45b7d1',
      braider: '#96ceb4',
      user: '#feca57'
    }
    return colors[role] || '#95a5a6'
  }

  const getRoleIcon = (role) => {
    const icons = {
      dev: '⚙️',
      admin: '👑',
      artist: '🎨',
      braider: '💇‍♀️',
      user: '👤'
    }
    return icons[role] || '👤'
  }

  if (!hasAdminAccess) {
    return (
      <div className={styles.accessDenied}>
        <h3>Access Denied</h3>
        <p>You need admin privileges to view this dashboard.</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading dashboard data...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.error}>
        <h3>Error Loading Dashboard</h3>
        <p>{error}</p>
        <button onClick={fetchDashboardData} className={styles.retryButton}>
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h2>User Management Dashboard</h2>
        <button onClick={fetchDashboardData} className={styles.refreshButton}>
          🔄 Refresh
        </button>
      </div>

      {/* Statistics Cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>👥</div>
          <div className={styles.statContent}>
            <h3>{stats.totalUsers}</h3>
            <p>Total Users</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <h3>{stats.activeUsers}</h3>
            <p>Active Users</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>📈</div>
          <div className={styles.statContent}>
            <h3>{stats.newThisMonth}</h3>
            <p>New This Month</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⏳</div>
          <div className={styles.statContent}>
            <h3>{stats.pendingApplications}</h3>
            <p>Pending Applications</p>
          </div>
        </div>
      </div>

      {/* Role Breakdown */}
      <div className={styles.section}>
        <h3>Role Distribution</h3>
        <div className={styles.roleBreakdown}>
          {Object.entries(stats.roleBreakdown).map(([role, count]) => (
            <div key={role} className={styles.roleCard}>
              <div
                className={styles.roleIcon}
                style={{ backgroundColor: getRoleColor(role) }}
              >
                {getRoleIcon(role)}
              </div>
              <div className={styles.roleInfo}>
                <h4>{role.charAt(0).toUpperCase() + role.slice(1)}</h4>
                <p>{count} users</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className={styles.section}>
        <h3>Recent Activity</h3>
        <div className={styles.activityList}>
          {recentActivity.length === 0 ? (
            <p className={styles.noActivity}>No recent activity</p>
          ) : (
            recentActivity.map((activity, index) => (
              <div key={index} className={styles.activityItem}>
                <div className={styles.activityIcon}>
                  {activity.type === 'user_created' && '➕'}
                  {activity.type === 'user_login' && '🔐'}
                  {activity.type === 'role_changed' && '🔄'}
                  {activity.type === 'application_submitted' && '📝'}
                </div>
                <div className={styles.activityContent}>
                  <p className={styles.activityDescription}>
                    {activity.description}
                  </p>
                  <p className={styles.activityTime}>
                    {new Date(activity.created_at).toLocaleString()}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.section}>
        <h3>Quick Actions</h3>
        <div className={styles.quickActions}>
          <button className={styles.actionButton}>
            👥 View All Users
          </button>
          <button className={styles.actionButton}>
            📝 Review Applications
          </button>
          <button className={styles.actionButton}>
            📊 Export User Data
          </button>
          <button className={styles.actionButton}>
            ⚙️ User Settings
          </button>
        </div>
      </div>
    </div>
  )
}
