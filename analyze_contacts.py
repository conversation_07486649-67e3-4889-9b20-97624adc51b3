#!/usr/bin/env python3
"""
Google Contacts CSV Analyzer for Ocean Soul Sparkles
Analyzes Google Contacts export and identifies customers with Square Customer IDs
"""

import csv
import json
import re
from datetime import datetime
from collections import defaultdict

class ContactsAnalyzer:
    def __init__(self):
        self.contacts = []
        self.square_customers = []
        self.statistics = {
            'total_contacts': 0,
            'contacts_with_square_id': 0,
            'valid_emails': 0,
            'valid_phones': 0,
            'duplicates_found': 0,
            'data_quality_issues': []
        }
    
    def analyze_csv(self, filename='contacts .Google csv.csv'):
        """Main analysis function"""
        print("🚀 Starting Google Contacts analysis...")
        
        try:
            # Read CSV file
            self.read_csv(filename)
            
            # Find contacts with Square Customer IDs
            self.find_square_customers()
            
            # Analyze data quality
            self.analyze_data_quality()
            
            # Detect duplicates
            self.detect_duplicates()
            
            # Generate reports
            self.generate_reports()
            
            # Print summary
            self.print_summary()
            
        except Exception as e:
            print(f"❌ Error analyzing contacts: {e}")
            raise
    
    def read_csv(self, filename):
        """Read and parse CSV file"""
        print(f"📖 Reading {filename}...")
        
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                self.contacts = list(reader)
                self.statistics['total_contacts'] = len(self.contacts)
                print(f"📊 Loaded {len(self.contacts)} contacts")
        except FileNotFoundError:
            print(f"❌ File {filename} not found")
            raise
        except Exception as e:
            print(f"❌ Error reading CSV: {e}")
            raise
    
    def find_square_customers(self):
        """Find contacts with Square Customer IDs"""
        print("🔍 Finding contacts with Square Customer IDs...")
        
        square_id_fields = [
            'Custom Field 9 - Value',  # Based on CSV structure
            'Square Customer ID'       # Alternative field name
        ]
        
        for contact in self.contacts:
            square_id = None
            
            # Check multiple possible fields for Square Customer ID
            for field in square_id_fields:
                if field in contact and contact[field] and contact[field].strip():
                    square_id = contact[field].strip()
                    break
            
            # Also check custom fields by label
            for i in range(1, 17):  # Custom Field 1-16
                label_field = f'Custom Field {i} - Label'
                value_field = f'Custom Field {i} - Value'
                
                if (label_field in contact and 
                    contact[label_field] == 'Square Customer ID' and
                    value_field in contact and 
                    contact[value_field] and 
                    contact[value_field].strip()):
                    square_id = contact[value_field].strip()
                    break
            
            if square_id:
                contact['square_customer_id'] = square_id
                self.square_customers.append(contact)
        
        self.statistics['contacts_with_square_id'] = len(self.square_customers)
        print(f"💳 Found {len(self.square_customers)} contacts with Square Customer IDs")
    
    def analyze_data_quality(self):
        """Analyze data quality for Square customers"""
        print("🧹 Analyzing data quality...")
        
        for i, contact in enumerate(self.square_customers):
            # Check email
            email = contact.get('E-mail 1 - Value', '').strip()
            if email:
                if self.is_valid_email(email):
                    self.statistics['valid_emails'] += 1
                else:
                    self.statistics['data_quality_issues'].append({
                        'index': i,
                        'issue': f'Invalid email format: {email}',
                        'contact': self.get_contact_name(contact)
                    })
            else:
                self.statistics['data_quality_issues'].append({
                    'index': i,
                    'issue': 'Missing email address',
                    'contact': self.get_contact_name(contact)
                })
            
            # Check phone
            phone = contact.get('Phone 1 - Value', '').strip()
            if phone:
                if self.is_valid_australian_phone(phone):
                    self.statistics['valid_phones'] += 1
                else:
                    self.statistics['data_quality_issues'].append({
                        'index': i,
                        'issue': f'Invalid phone format: {phone}',
                        'contact': self.get_contact_name(contact)
                    })
    
    def is_valid_email(self, email):
        """Basic email validation"""
        pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        return re.match(pattern, email.lower()) is not None
    
    def is_valid_australian_phone(self, phone):
        """Check if phone number is valid Australian format"""
        # Remove all non-digit characters except +
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # Valid Australian phone patterns
        patterns = [
            r'^\+61[2-9]\d{8}$',      # +61 landline
            r'^\+614\d{8}$',          # +61 mobile
            r'^61[2-9]\d{8}$',        # 61 landline (missing +)
            r'^614\d{8}$',            # 61 mobile (missing +)
            r'^0[2-9]\d{8}$',         # 0 landline
            r'^04\d{8}$'              # 04 mobile
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    def get_contact_name(self, contact):
        """Get contact name for reporting"""
        first_name = contact.get('First Name', '').strip()
        last_name = contact.get('Last Name', '').strip()
        
        if first_name or last_name:
            return f"{first_name} {last_name}".strip()
        
        # Fallback to organization name
        return contact.get('Organization Name', 'Unknown').strip()
    
    def detect_duplicates(self):
        """Detect potential duplicate customers"""
        print("🔍 Detecting duplicates...")
        
        email_map = defaultdict(list)
        phone_map = defaultdict(list)
        
        for i, contact in enumerate(self.square_customers):
            email = contact.get('E-mail 1 - Value', '').strip().lower()
            phone = contact.get('Phone 1 - Value', '').strip()
            
            if email:
                email_map[email].append(i)
            
            if phone:
                # Normalize phone for comparison
                clean_phone = re.sub(r'[^\d+]', '', phone)
                phone_map[clean_phone].append(i)
        
        # Count duplicates
        duplicates = 0
        for email, indices in email_map.items():
            if len(indices) > 1:
                duplicates += 1
        
        for phone, indices in phone_map.items():
            if len(indices) > 1:
                duplicates += 1
        
        self.statistics['duplicates_found'] = duplicates
    
    def generate_reports(self):
        """Generate analysis reports"""
        print("📄 Generating reports...")
        
        # Generate summary report
        summary_report = {
            'analysis_date': datetime.now().isoformat(),
            'summary': self.statistics,
            'square_customers_sample': self.get_sample_customers(5),
            'recommendations': self.generate_recommendations()
        }
        
        with open('contacts_analysis_report.json', 'w') as f:
            json.dump(summary_report, f, indent=2)
        
        # Generate detailed Square customers list
        square_customers_data = []
        for contact in self.square_customers:
            customer_data = {
                'name': self.get_contact_name(contact),
                'email': contact.get('E-mail 1 - Value', '').strip(),
                'phone': contact.get('Phone 1 - Value', '').strip(),
                'square_customer_id': contact.get('square_customer_id', ''),
                'address': contact.get('Address 1 - Formatted', '').strip(),
                'custom_fields': self.extract_custom_fields(contact)
            }
            square_customers_data.append(customer_data)
        
        with open('square_customers_detailed.json', 'w') as f:
            json.dump(square_customers_data, f, indent=2)
        
        print("✅ Generated reports: contacts_analysis_report.json, square_customers_detailed.json")
    
    def get_sample_customers(self, count=5):
        """Get sample of Square customers for preview"""
        sample = []
        for i, contact in enumerate(self.square_customers[:count]):
            sample.append({
                'index': i,
                'name': self.get_contact_name(contact),
                'email': contact.get('E-mail 1 - Value', '').strip(),
                'square_id': contact.get('square_customer_id', ''),
                'created_at': self.extract_custom_field_value(contact, 'Created At')
            })
        return sample
    
    def extract_custom_fields(self, contact):
        """Extract all custom fields for a contact"""
        custom_fields = {}
        
        for i in range(1, 17):
            label_field = f'Custom Field {i} - Label'
            value_field = f'Custom Field {i} - Value'
            
            label = contact.get(label_field, '').strip()
            value = contact.get(value_field, '').strip()
            
            if label and value:
                custom_fields[label] = value
        
        return custom_fields
    
    def extract_custom_field_value(self, contact, field_name):
        """Extract specific custom field value"""
        for i in range(1, 17):
            label_field = f'Custom Field {i} - Label'
            value_field = f'Custom Field {i} - Value'
            
            if (contact.get(label_field, '').strip() == field_name and
                contact.get(value_field, '').strip()):
                return contact.get(value_field, '').strip()
        
        return None
    
    def generate_recommendations(self):
        """Generate recommendations for import"""
        recommendations = []
        
        if self.statistics['data_quality_issues']:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Data Quality',
                'issue': f"{len(self.statistics['data_quality_issues'])} data quality issues found",
                'action': 'Review and clean data before import'
            })
        
        if self.statistics['duplicates_found'] > 0:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Duplicates',
                'issue': f"{self.statistics['duplicates_found']} potential duplicates found",
                'action': 'Review and merge duplicate records'
            })
        
        recommendations.append({
            'priority': 'INFO',
            'category': 'Import Strategy',
            'issue': 'Square Customer ID integration',
            'action': 'Ensure Square Customer IDs are preserved during import for payment integration'
        })
        
        return recommendations
    
    def print_summary(self):
        """Print analysis summary"""
        print('\n📊 ANALYSIS SUMMARY')
        print('=' * 50)
        print(f"📋 Total contacts: {self.statistics['total_contacts']}")
        print(f"💳 Contacts with Square IDs: {self.statistics['contacts_with_square_id']}")
        print(f"📧 Valid emails: {self.statistics['valid_emails']}")
        print(f"📱 Valid phones: {self.statistics['valid_phones']}")
        print(f"🔄 Potential duplicates: {self.statistics['duplicates_found']}")
        print(f"⚠️  Data quality issues: {len(self.statistics['data_quality_issues'])}")
        
        print('\n📁 REPORTS GENERATED:')
        print('• contacts_analysis_report.json - Summary analysis')
        print('• square_customers_detailed.json - Detailed Square customer data')
        
        if self.statistics['data_quality_issues']:
            print('\n⚠️  DATA QUALITY ISSUES:')
            for issue in self.statistics['data_quality_issues'][:5]:  # Show first 5
                print(f"  • {issue['contact']}: {issue['issue']}")
            if len(self.statistics['data_quality_issues']) > 5:
                print(f"  ... and {len(self.statistics['data_quality_issues']) - 5} more")
        
        print('\n🚀 NEXT STEPS:')
        print('1. Review generated reports')
        print('2. Clean data quality issues')
        print('3. Handle duplicate records')
        print('4. Prepare for Supabase import')

if __name__ == '__main__':
    analyzer = ContactsAnalyzer()
    analyzer.analyze_csv()
