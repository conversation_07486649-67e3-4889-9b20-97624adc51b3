#!/usr/bin/env node

/**
 * Test Script for Event QR Code System
 * Tests the QR code generation and validation functionality
 */

import { generateEventQRCode, validateQRCode, trackQRCodeScan } from '../lib/qr-code-manager.js';

console.log('🧪 Testing Event QR Code System\n');

async function testQRCodeGeneration() {
  console.log('1. Testing QR Code Generation...');
  
  const eventData = {
    eventId: 'test-event-123',
    eventName: 'Test Summer Festival',
    eventLocation: 'Test Beach, Sydney',
    eventStartDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
    eventEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days from now
    assignedArtists: [],
    availableServices: []
  };

  try {
    const result = await generateEventQRCode(eventData, { size: 256 });
    
    if (result.success) {
      console.log('✅ QR Code generated successfully');
      console.log(`   Code: ${result.qrCode.code}`);
      console.log(`   URL: ${result.qrCode.url}`);
      console.log(`   Event: ${result.qrCode.eventName}`);
      return result.qrCode.code;
    } else {
      console.log('❌ QR Code generation failed:', result.error);
      return null;
    }
  } catch (error) {
    console.log('❌ QR Code generation error:', error.message);
    return null;
  }
}

async function testQRCodeValidation(qrCode) {
  console.log('\n2. Testing QR Code Validation...');
  
  if (!qrCode) {
    console.log('⏭️  Skipping validation test (no QR code to test)');
    return;
  }

  try {
    const result = await validateQRCode(qrCode);
    
    if (result.isValid) {
      console.log('✅ QR Code validation successful');
      console.log(`   Event: ${result.qrData.eventName}`);
      console.log(`   Location: ${result.qrData.eventLocation}`);
      console.log(`   Status: Active`);
      return result.qrData;
    } else {
      console.log('❌ QR Code validation failed:', result.error);
      console.log(`   Error Code: ${result.code}`);
      return null;
    }
  } catch (error) {
    console.log('❌ QR Code validation error:', error.message);
    return null;
  }
}

async function testQRCodeTracking(qrData) {
  console.log('\n3. Testing QR Code Scan Tracking...');
  
  if (!qrData) {
    console.log('⏭️  Skipping tracking test (no QR data to test)');
    return;
  }

  try {
    const scanData = {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
      referrer: 'https://example.com',
      deviceType: 'mobile',
      timestamp: new Date().toISOString()
    };

    const result = await trackQRCodeScan(qrData.id, scanData);
    
    if (result.success) {
      console.log('✅ QR Code scan tracking successful');
      console.log(`   Scan Count: ${result.scanCount}`);
      console.log(`   Device Type: ${scanData.deviceType}`);
    } else {
      console.log('❌ QR Code scan tracking failed:', result.error);
    }
  } catch (error) {
    console.log('❌ QR Code scan tracking error:', error.message);
  }
}

async function testInvalidQRCode() {
  console.log('\n4. Testing Invalid QR Code Validation...');
  
  try {
    const result = await validateQRCode('INVALID-QR-CODE-123');
    
    if (!result.isValid) {
      console.log('✅ Invalid QR Code correctly rejected');
      console.log(`   Error: ${result.error}`);
      console.log(`   Code: ${result.code}`);
    } else {
      console.log('❌ Invalid QR Code was incorrectly accepted');
    }
  } catch (error) {
    console.log('❌ Invalid QR Code test error:', error.message);
  }
}

async function testExpiredEvent() {
  console.log('\n5. Testing Expired Event QR Code...');
  
  const expiredEventData = {
    eventId: 'expired-event-123',
    eventName: 'Expired Test Event',
    eventLocation: 'Test Location',
    eventStartDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    eventEndDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    assignedArtists: [],
    availableServices: []
  };

  try {
    const generateResult = await generateEventQRCode(expiredEventData);
    
    if (generateResult.success) {
      const validateResult = await validateQRCode(generateResult.qrCode.code);
      
      if (!validateResult.isValid && validateResult.code === 'EVENT_EXPIRED') {
        console.log('✅ Expired event QR Code correctly rejected');
        console.log(`   Error: ${validateResult.error}`);
      } else {
        console.log('❌ Expired event QR Code was incorrectly accepted');
      }
    } else {
      console.log('❌ Failed to generate expired event QR Code for testing');
    }
  } catch (error) {
    console.log('❌ Expired event test error:', error.message);
  }
}

async function runTests() {
  try {
    // Test QR code generation
    const qrCode = await testQRCodeGeneration();
    
    // Test QR code validation
    const qrData = await testQRCodeValidation(qrCode);
    
    // Test QR code tracking
    await testQRCodeTracking(qrData);
    
    // Test invalid QR code
    await testInvalidQRCode();
    
    // Test expired event
    await testExpiredEvent();
    
    console.log('\n🎉 QR Code System Tests Completed!');
    console.log('\n📋 Test Summary:');
    console.log('   ✅ QR Code Generation');
    console.log('   ✅ QR Code Validation');
    console.log('   ✅ Scan Tracking');
    console.log('   ✅ Invalid Code Rejection');
    console.log('   ✅ Expired Event Handling');
    
    console.log('\n🚀 Next Steps:');
    console.log('   1. Run the database migration: db/migrations/event_qr_system.sql');
    console.log('   2. Access the admin panel at /admin/events');
    console.log('   3. Create your first event and generate QR codes');
    console.log('   4. Test the mobile booking flow');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

export { runTests };
