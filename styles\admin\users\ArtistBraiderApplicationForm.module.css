.formContainer {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form {
  padding: 30px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.header h2 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 1.8rem;
}

.header p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

.formGroup {
  margin-bottom: 25px;
}

.formGroup label {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.input,
.textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s ease;
  font-family: inherit;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

.radioGroup {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radioLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  cursor: pointer;
  padding: 10px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.radioLabel:hover {
  border-color: #6e8efb;
  background: #f8f9ff;
}

.radioLabel input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #6e8efb;
}

.radioLabel input[type="radio"]:checked + span {
  color: #6e8efb;
  font-weight: 600;
}

.checkboxGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.checkboxLabel:hover {
  background: #f8f9ff;
}

.checkboxLabel input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #6e8efb;
}

.availabilitySection {
  margin: 15px 0;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.availabilitySection h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1rem;
}

.availabilityOptions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 15px;
}

.charCount {
  font-size: 0.85rem;
  color: #666;
  margin-top: 5px;
  text-align: right;
}

.error {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 5px;
  font-weight: 500;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #f0f0f0;
}

.cancelButton,
.submitButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancelButton {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #e1e5e9;
}

.cancelButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.submitButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: 2px solid transparent;
}

.submitButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .formContainer {
    margin: 10px;
    border-radius: 8px;
  }
  
  .form {
    padding: 20px;
  }
  
  .header h2 {
    font-size: 1.5rem;
  }
  
  .radioGroup {
    flex-direction: column;
    gap: 10px;
  }
  
  .checkboxGrid {
    grid-template-columns: 1fr;
  }
  
  .formActions {
    flex-direction: column;
  }
  
  .cancelButton,
  .submitButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .form {
    padding: 15px;
  }
  
  .header {
    margin-bottom: 20px;
  }
  
  .formGroup {
    margin-bottom: 20px;
  }
}
