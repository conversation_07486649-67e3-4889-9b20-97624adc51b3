/**
 * Booking Availability API Endpoint
 * 
 * Provides available time slots for booking services, considering:
 * - Existing bookings
 * - Artist availability schedules
 * - Service duration requirements
 * - Event conflicts
 * 
 * GET /api/bookings/availability?date=YYYY-MM-DD&service_id=UUID&duration=30
 */

import { getAdminClient } from '@/lib/supabase';
import { withAdminAuth } from '@/lib/admin-auth';

/**
 * Generate time slots for a given date and duration
 * @param {Date} date - The date to generate slots for
 * @param {number} duration - Service duration in minutes
 * @param {number} startHour - Business start hour (default: 8)
 * @param {number} endHour - Business end hour (default: 20)
 * @returns {Array} Array of time slot objects
 */
function generateTimeSlots(date, duration, startHour = 8, endHour = 20) {
  const slots = [];
  const slotInterval = 15; // 15-minute intervals
  
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += slotInterval) {
      const startTime = new Date(date);
      startTime.setHours(hour, minute, 0, 0);
      
      const endTime = new Date(startTime);
      endTime.setMinutes(endTime.getMinutes() + duration);
      
      // Don't create slots that extend beyond business hours
      if (endTime.getHours() <= endHour) {
        slots.push({
          id: `${startTime.getTime()}`,
          start_time: startTime.toISOString(),
          end_time: endTime.toISOString(),
          available: true,
          artist: null
        });
      }
    }
  }
  
  return slots;
}

/**
 * Check for booking conflicts in the given time range
 * @param {Object} supabase - Supabase client
 * @param {string} startTime - Start time ISO string
 * @param {string} endTime - End time ISO string
 * @param {string} serviceId - Service ID (optional)
 * @returns {Array} Array of conflicting bookings
 */
async function getBookingConflicts(supabase, startTime, endTime, serviceId = null) {
  let query = supabase
    .from('bookings')
    .select(`
      id,
      start_time,
      end_time,
      service_id,
      customers(name),
      services(name)
    `)
    .neq('status', 'canceled')
    .or(`and(start_time.lte.${startTime},end_time.gt.${startTime}),and(start_time.lt.${endTime},end_time.gte.${endTime}),and(start_time.gte.${startTime},end_time.lte.${endTime})`);

  // If service_id is provided, only check conflicts for the same service
  if (serviceId) {
    query = query.eq('service_id', serviceId);
  }

  const { data, error } = await query;
  
  if (error) {
    throw error;
  }
  
  return data || [];
}

/**
 * Get available artists for a service
 * @param {Object} supabase - Supabase client
 * @param {string} serviceId - Service ID
 * @returns {Array} Array of available artists
 */
async function getAvailableArtists(supabase, serviceId) {
  try {
    // First, try to get artists from the artist management system
    const { data: artists, error } = await supabase
      .from('artist_availability_view')
      .select('*')
      .eq('is_active', true)
      .eq('is_available_today', true);

    if (error) {
      console.warn('Artist availability view not available, using fallback');
      // Fallback: return a default "any artist" option
      return [{
        artist_id: 'any',
        artist_name: 'Any Available Artist',
        display_name: 'Any Available Artist'
      }];
    }

    // Filter artists who can provide this service
    const serviceArtists = artists?.filter(artist => {
      const specializations = artist.service_specializations || [];
      return specializations.some(spec => spec.service_id === serviceId);
    }) || [];

    // Always include "any artist" option
    const anyArtistOption = {
      artist_id: 'any',
      artist_name: 'Any Available Artist',
      display_name: 'Any Available Artist'
    };

    return [anyArtistOption, ...serviceArtists];
  } catch (error) {
    console.warn('Error fetching artists:', error);
    // Return default option if artist system is not set up
    return [{
      artist_id: 'any',
      artist_name: 'Any Available Artist',
      display_name: 'Any Available Artist'
    }];
  }
}

async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { date, service_id, duration = 30 } = req.query;

    // Validate required parameters
    if (!date) {
      return res.status(400).json({ error: 'Date parameter is required (YYYY-MM-DD format)' });
    }

    if (!service_id) {
      return res.status(400).json({ error: 'Service ID parameter is required' });
    }

    // Parse and validate date
    const requestedDate = new Date(date);
    if (isNaN(requestedDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Validate duration
    const serviceDuration = parseInt(duration);
    if (isNaN(serviceDuration) || serviceDuration <= 0) {
      return res.status(400).json({ error: 'Invalid duration. Must be a positive number' });
    }

    const supabase = getAdminClient();

    // Verify service exists
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('id, name, duration, price')
      .eq('id', service_id)
      .single();

    if (serviceError || !service) {
      return res.status(404).json({ error: 'Service not found' });
    }

    // Use service duration if not provided in query
    const actualDuration = serviceDuration || service.duration;

    // Generate all possible time slots for the date
    const allSlots = generateTimeSlots(requestedDate, actualDuration);

    // Get booking conflicts for the entire day
    const dayStart = new Date(requestedDate);
    dayStart.setHours(0, 0, 0, 0);
    const dayEnd = new Date(requestedDate);
    dayEnd.setHours(23, 59, 59, 999);

    const conflicts = await getBookingConflicts(
      supabase,
      dayStart.toISOString(),
      dayEnd.toISOString(),
      service_id
    );

    // Get available artists for this service
    const availableArtists = await getAvailableArtists(supabase, service_id);

    // Mark slots as unavailable if they conflict with existing bookings
    const availableSlots = allSlots.map(slot => {
      const slotStart = new Date(slot.start_time);
      const slotEnd = new Date(slot.end_time);

      // Check if this slot conflicts with any existing booking
      const hasConflict = conflicts.some(conflict => {
        const conflictStart = new Date(conflict.start_time);
        const conflictEnd = new Date(conflict.end_time);

        // Check for overlap
        return (slotStart < conflictEnd && slotEnd > conflictStart);
      });

      return {
        ...slot,
        available: !hasConflict,
        artist: hasConflict ? null : availableArtists[0], // Assign default artist if available
        conflicts: hasConflict ? conflicts.filter(conflict => {
          const conflictStart = new Date(conflict.start_time);
          const conflictEnd = new Date(conflict.end_time);
          return (slotStart < conflictEnd && slotEnd > conflictStart);
        }) : []
      };
    });

    // Filter to only return slots that are in the future (with 1 hour grace period)
    const now = new Date();
    const gracePeriod = 60 * 60 * 1000; // 1 hour
    const cutoffTime = new Date(now.getTime() - gracePeriod);

    const futureSlots = availableSlots.filter(slot => {
      return new Date(slot.start_time) > cutoffTime;
    });

    return res.status(200).json({
      success: true,
      date: date,
      service: {
        id: service.id,
        name: service.name,
        duration: actualDuration,
        price: service.price
      },
      availability: futureSlots,
      available_artists: availableArtists,
      total_slots: futureSlots.length,
      available_slots: futureSlots.filter(slot => slot.available).length,
      booked_slots: futureSlots.filter(slot => !slot.available).length
    });

  } catch (error) {
    console.error('Error fetching availability:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}

// Apply admin authentication middleware
export default withAdminAuth(handler);
