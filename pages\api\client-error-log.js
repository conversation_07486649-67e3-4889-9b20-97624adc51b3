/**
 * API endpoint to receive and log client-side errors
 * This allows us to see React Error #130 and other client-side errors in the server terminal
 */

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { error, timestamp, page, userAgent } = req.body;
    
    // Log the client-side error to the server console with clear formatting
    console.log('\n🚨 CLIENT-SIDE ERROR DETECTED:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📅 Timestamp: ${timestamp}`);
    console.log(`📍 Page: ${page}`);
    console.log(`🔍 Error: ${error}`);
    console.log(`🌐 User Agent: ${userAgent}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');

    // Check if it's a React Error #130
    if (error.includes('Objects are not valid as a React child') || 
        error.includes('Error #130') || 
        error.includes('found object with keys')) {
      console.log('⚠️  REACT ERROR #130 DETECTED - Objects being rendered as React children!');
      console.log('🔧 This indicates that an object is being passed directly to a React component');
      console.log('💡 Check for missing safeRender() calls or object data in JSX\n');
    }

    res.status(200).json({ success: true, logged: true });
  } catch (err) {
    console.error('Error logging client-side error:', err);
    res.status(500).json({ error: 'Failed to log error' });
  }
}
