#!/usr/bin/env node

/**
 * Test script for cache-control headers implementation
 * 
 * This script tests various API endpoints to verify that the correct
 * cache-control headers are being applied based on the data type and
 * request characteristics.
 * 
 * Usage: node scripts/test-cache-headers.js
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const TIMEOUT = 5000;

// Test cases with expected cache strategies
const TEST_CASES = [
  {
    name: 'Public Products (Semi-Static)',
    url: '/api/public/products',
    expectedCache: 'max-age=300, stale-while-revalidate=60',
    description: 'Products should be cached for 5 minutes'
  },
  {
    name: 'Public Services (Static)',
    url: '/api/public/services',
    expectedCache: 'max-age=3600, stale-while-revalidate=300',
    description: 'Services should be cached for 1 hour'
  },
  {
    name: 'Admin Customers (Admin)',
    url: '/api/admin/customers',
    expectedCache: 'max-age=60, no-cache',
    description: 'Admin customer data should have short cache'
  },
  {
    name: 'Admin Bookings (Admin)',
    url: '/api/admin/bookings',
    expectedCache: 'max-age=60, no-cache',
    description: 'Regular admin bookings should have short cache'
  },
  {
    name: 'Pending Bookings (Real-Time)',
    url: '/api/admin/bookings?status=pending',
    expectedCache: 'no-cache, no-store, must-revalidate',
    description: 'Pending bookings should never be cached'
  },
  {
    name: 'Completed Payments (Real-Time)',
    url: '/api/admin/payments?payment_status=completed',
    expectedCache: 'no-cache, no-store, must-revalidate',
    description: 'Payment data should never be cached'
  }
];

/**
 * Make HTTP request and return headers
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const fullUrl = `${BASE_URL}${url}`;
    const lib = fullUrl.startsWith('https:') ? https : http;
    
    const req = lib.request(fullUrl, { method: 'HEAD' }, (res) => {
      resolve({
        statusCode: res.statusCode,
        headers: res.headers,
        cacheControl: res.headers['cache-control'] || null
      });
    });
    
    req.on('error', reject);
    req.setTimeout(TIMEOUT, () => {
      req.destroy();
      reject(new Error(`Request timeout after ${TIMEOUT}ms`));
    });
    
    req.end();
  });
}

/**
 * Test a single endpoint
 */
async function testEndpoint(testCase) {
  try {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    console.log(`   URL: ${testCase.url}`);
    console.log(`   Expected: ${testCase.expectedCache}`);
    
    const result = await makeRequest(testCase.url);
    
    if (result.statusCode !== 200) {
      console.log(`   ❌ FAIL: HTTP ${result.statusCode}`);
      return false;
    }
    
    const actualCache = result.cacheControl;
    
    if (!actualCache) {
      console.log(`   ❌ FAIL: No Cache-Control header found`);
      return false;
    }
    
    if (actualCache === testCase.expectedCache) {
      console.log(`   ✅ PASS: ${actualCache}`);
      return true;
    } else {
      console.log(`   ❌ FAIL: Got "${actualCache}"`);
      return false;
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('🚀 Starting Cache-Control Headers Test Suite');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log('=' * 60);
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of TEST_CASES) {
    const success = await testEndpoint(testCase);
    if (success) {
      passed++;
    } else {
      failed++;
    }
  }
  
  console.log('\n' + '=' * 60);
  console.log('📊 Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Cache-control headers are working correctly.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

/**
 * Display help information
 */
function showHelp() {
  console.log(`
Cache-Control Headers Test Script

Usage:
  node scripts/test-cache-headers.js [options]

Options:
  --help, -h     Show this help message
  --url <url>    Set base URL (default: http://localhost:3000)

Environment Variables:
  TEST_BASE_URL  Base URL for testing (default: http://localhost:3000)

Examples:
  node scripts/test-cache-headers.js
  node scripts/test-cache-headers.js --url https://www.oceansoulsparkles.com.au
  TEST_BASE_URL=https://staging.example.com node scripts/test-cache-headers.js
`);
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  showHelp();
  process.exit(0);
}

const urlIndex = args.indexOf('--url');
if (urlIndex !== -1 && args[urlIndex + 1]) {
  process.env.TEST_BASE_URL = args[urlIndex + 1];
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test suite crashed:', error);
  process.exit(1);
});
