# Production Environment Configuration
# This file should be used for production deployments

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ndlgbcsbidyhxbpqzgqp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzA1OTk5NywiZXhwIjoyMDYyNjM1OTk3fQ._9quzbWREyhdPiQiSUkzuqyBm8v4fkK2uqiswdt3AvY

# Site Configuration - Production URLs
NEXT_PUBLIC_SITE_URL=https://www.oceansoulsparkles.com.au
NEXT_PUBLIC_ADMIN_URL=https://www.oceansoulsparkles.com.au/admin

# OneSignal Configuration (Optional)
NEXT_PUBLIC_ONESIGNAL_APP_ID=
ONESIGNAL_API_KEY=
ONESIGNAL_REST_API_KEY=

# Production Security Configuration
NEXT_PUBLIC_DEV_MODE=false
NEXT_PUBLIC_DEBUG_AUTH=false
ENABLE_AUTH_BYPASS=false

# Square Configuration - Update these for production
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-zmeKEI4JNUCFsS4wkL7jjQ
NEXT_PUBLIC_SQUARE_LOCATION_ID=L2DSKTPV3D3YT
SQUARE_ACCESS_TOKEN=****************************************************************
SQUARE_ENVIRONMENT=sandbox  # Change to 'production' when ready

# Security Headers
NEXT_PUBLIC_ENABLE_SECURITY_HEADERS=true
NEXT_PUBLIC_DISABLE_CONSOLE_LOGS=true
