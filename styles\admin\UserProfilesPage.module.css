.userProfilesPage {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8f9fa;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.headerContent h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.headerContent p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.roleIndicator {
  display: flex;
  align-items: center;
}

.devBadge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.errorAlert {
  display: flex;
  align-items: center;
  gap: 15px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 15px 20px;
  margin-bottom: 20px;
  color: #721c24;
}

.errorIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.errorContent h3 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
}

.errorContent p {
  margin: 0;
  font-size: 0.9rem;
}

.errorClose {
  background: none;
  border: none;
  color: #721c24;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.errorClose:hover {
  background: rgba(114, 28, 36, 0.1);
}

.tabNavigation {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.tab {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.tab:hover {
  border-color: #6e8efb;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(110, 142, 251, 0.15);
}

.tab.activeTab {
  border-color: #6e8efb;
  background: linear-gradient(135deg, #f8f9ff, #ffffff);
  box-shadow: 0 4px 15px rgba(110, 142, 251, 0.2);
}

.tabHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.tabIcon {
  font-size: 1.5rem;
}

.tabLabel {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.tabDescription {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.tabContent {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  position: relative;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 10;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.helpSection {
  margin-top: 30px;
}

.helpCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.helpCard h3 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.helpContent {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.helpItem {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.helpItem strong {
  color: #fff;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .userProfilesPage {
    padding: 15px;
  }
  
  .header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .headerContent h1 {
    font-size: 1.6rem;
  }
  
  .tabNavigation {
    grid-template-columns: 1fr;
  }
  
  .helpContent {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .userProfilesPage {
    padding: 10px;
  }
  
  .header {
    padding: 20px;
  }
  
  .tab {
    padding: 15px;
  }
  
  .tabHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
