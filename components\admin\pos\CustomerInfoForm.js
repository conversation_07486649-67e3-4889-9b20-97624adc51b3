import { useState } from 'react'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/POS.module.css'

/**
 * CustomerInfoForm component for quick customer data collection in POS
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onCustomerData - Callback with customer data
 * @param {boolean} props.isLoading - Loading state
 * @returns {JSX.Element}
 */
export default function CustomerInfoForm({ onCustomerData, isLoading = false }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    marketingConsent: false,
    isGuest: true
  })
  const [errors, setErrors] = useState({})

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    // Name is required
    if (!formData.name.trim()) {
      newErrors.name = 'Customer name is required'
    }

    // Email validation (optional but must be valid if provided)
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    // Phone validation (optional but must be valid if provided)
    if (formData.phone && !/^[\d\s\-\+\(\)]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (validateForm()) {
      onCustomerData(formData)
    }
  }

  const handleSkip = () => {
    // Provide minimal customer data for anonymous transactions
    onCustomerData({
      name: 'Walk-in Customer',
      email: '',
      phone: '',
      marketingConsent: false,
      isGuest: true,
      isAnonymous: true
    })
  }

  return (
    <div className={styles.customerForm}>
      <div className={styles.customerFormHeader}>
        <h3>Customer Information</h3>
        <p>Collect customer details for this transaction (optional)</p>
      </div>

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className={styles.formGroup}>
          <label htmlFor="name" className={styles.label}>
            Customer Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className={`${styles.input} ${errors.name ? styles.inputError : ''}`}
            placeholder="Enter customer name"
            disabled={isLoading}
          />
          {errors.name && (
            <span className={styles.errorText}>{errors.name}</span>
          )}
        </div>

        <div className={styles.formRow}>
          <div className={styles.formGroup}>
            <label htmlFor="email" className={styles.label}>
              Email Address
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`${styles.input} ${errors.email ? styles.inputError : ''}`}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
            {errors.email && (
              <span className={styles.errorText}>{errors.email}</span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="phone" className={styles.label}>
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className={`${styles.input} ${errors.phone ? styles.inputError : ''}`}
              placeholder="0400 000 000"
              disabled={isLoading}
            />
            {errors.phone && (
              <span className={styles.errorText}>{errors.phone}</span>
            )}
          </div>
        </div>

        <div className={styles.formGroup}>
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              name="marketingConsent"
              checked={formData.marketingConsent}
              onChange={handleInputChange}
              className={styles.checkbox}
              disabled={isLoading}
            />
            <span className={styles.checkboxText}>
              I would like to receive marketing emails about special offers and events
            </span>
          </label>
        </div>

        <div className={styles.formActions}>
          <button
            type="button"
            onClick={handleSkip}
            className={styles.skipButton}
            disabled={isLoading}
          >
            Skip (Anonymous)
          </button>
          
          <button
            type="submit"
            className={styles.continueButton}
            disabled={isLoading}
          >
            {isLoading ? 'Processing...' : 'Continue to Payment'}
          </button>
        </div>
      </form>

      <div className={styles.privacyNote}>
        <p>
          <strong>Privacy:</strong> Customer information is stored securely and used only for 
          booking management and marketing (if consented). We never share personal data with third parties.
        </p>
      </div>
    </div>
  )
}
