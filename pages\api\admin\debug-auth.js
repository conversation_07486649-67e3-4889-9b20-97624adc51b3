import { authenticateAdminRequest } from '@/lib/admin-auth';

/**
 * Debug authentication endpoint for production troubleshooting
 * Provides detailed authentication information to help diagnose issues
 * 
 * GET /api/admin/debug-auth - Debug authentication status with detailed logging
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Debug Auth API called: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      error: 'Method not allowed',
      message: `Method ${req.method} not allowed`
    });
  }

  try {
    // Log all headers for debugging
    console.log(`[${requestId}] Request headers:`, Object.keys(req.headers));
    console.log(`[${requestId}] Authorization header present:`, !!req.headers.authorization);
    console.log(`[${requestId}] X-Auth-Token header present:`, !!req.headers['x-auth-token']);
    console.log(`[${requestId}] Cookie header present:`, !!req.headers.cookie);
    
    // Log environment info
    console.log(`[${requestId}] Environment:`, process.env.NODE_ENV);
    console.log(`[${requestId}] Auth bypass enabled:`, process.env.ENABLE_AUTH_BYPASS === 'true');
    
    // Attempt authentication with detailed logging
    console.log(`[${requestId}] Starting detailed authentication process...`);
    const authResult = await authenticateAdminRequest(req);
    
    // Log the full authentication result
    console.log(`[${requestId}] Authentication result:`, {
      authorized: authResult.authorized,
      hasUser: !!authResult.user,
      userEmail: authResult.user?.email,
      role: authResult.role,
      errorMessage: authResult.error?.message
    });
    
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed - returning detailed error info`);
      return res.status(401).json({ 
        error: 'Authentication failed',
        message: authResult.error?.message || 'Unknown authentication error',
        details: {
          hasUser: !!authResult.user,
          userEmail: authResult.user?.email || 'none',
          role: authResult.role || 'none',
          authorized: authResult.authorized,
          environment: process.env.NODE_ENV,
          authBypass: process.env.ENABLE_AUTH_BYPASS === 'true'
        },
        requestId,
        timestamp: new Date().toISOString()
      });
    }

    console.log(`[${requestId}] Authentication successful - returning success info`);

    // Return detailed success information
    return res.status(200).json({
      authenticated: true,
      user: {
        id: authResult.user.id,
        email: authResult.user.email
      },
      role: authResult.role,
      details: {
        environment: process.env.NODE_ENV,
        authBypass: process.env.ENABLE_AUTH_BYPASS === 'true',
        tokenSource: authResult.source || 'unknown'
      },
      requestId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error(`[${requestId}] Debug Auth API Error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Authentication debug failed',
      details: {
        errorMessage: error.message,
        errorStack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
