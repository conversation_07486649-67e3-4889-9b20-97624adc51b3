.adminLayout {
  display: grid;
  grid-template-columns: auto 1fr;
  grid-template-rows: 1fr;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background-color: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 100;
  transition: width 0.3s ease, transform 0.3s ease;
  height: 100vh;
  overflow-y: auto;
}

/* Collapsed sidebar state - icon-only mode */
.sidebar.collapsed {
  width: 70px;
}

.sidebarHeader {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: padding 0.3s ease;
}

.sidebar.collapsed .sidebarHeader {
  padding: 15px 10px;
  justify-content: space-between;
  flex-direction: column;
  gap: 10px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo {
  max-width: 150px;
  height: auto;
  transition: opacity 0.3s ease;
}

.compactLogo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6a0dad, #4ECDC4);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.compactLogo:hover {
  transform: scale(1.1);
}

.logoInitials {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
}

.collapseToggle {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapseToggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar.collapsed .collapseToggle {
  align-self: center;
}

.closeSidebar {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.sidebarNav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.navLink {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
}

.sidebar.collapsed .navLink {
  padding: 12px;
  justify-content: center;
  margin: 4px 8px;
  border-radius: 8px;
}

.navLink:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.navLink.active {
  background-color: #6a0dad;
  color: white;
}

.navLink svg {
  margin-right: 10px;
  flex-shrink: 0;
}

.sidebar.collapsed .navLink svg {
  margin-right: 0;
}

.navText {
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .navText {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Legacy support for existing navigation items */
.sidebarNav a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.2s;
}

.sidebar.collapsed .sidebarNav a {
  padding: 12px;
  justify-content: center;
  margin: 4px 8px;
  border-radius: 8px;
}

.sidebarNav a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebarNav a.active {
  background-color: #6a0dad;
  color: white;
}

.sidebarNav a svg {
  margin-right: 10px;
  flex-shrink: 0;
}

.sidebar.collapsed .sidebarNav a svg {
  margin-right: 0;
}

/* Hide text content in collapsed state for all navigation items */
.sidebar.collapsed .sidebarNav a {
  overflow: hidden;
  white-space: nowrap;
}

.sidebar.collapsed .sidebarNav a svg + * {
  opacity: 0;
  width: 0;
  margin-left: 0;
  transition: opacity 0.3s ease, width 0.3s ease;
}

/* Tooltip styles for collapsed sidebar */
.sidebar.collapsed .navLink[title]:hover::after,
.sidebar.collapsed .sidebarNav a[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: #2c3e50;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .navLink[title]:hover::before,
.sidebar.collapsed .sidebarNav a[title]:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: #2c3e50;
  z-index: 1001;
  margin-left: 4px;
}

.sidebarFooter {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  transition: padding 0.3s ease;
}

.sidebar.collapsed .sidebarFooter {
  padding: 15px 10px;
}

.userInfo {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  transition: opacity 0.3s ease;
}

.userName {
  font-weight: 500;
  margin-bottom: 5px;
}

.userRole {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
}

.signOutButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 4px;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  justify-content: center;
}

.sidebar.collapsed .signOutButton {
  padding: 12px;
  justify-content: center;
}

.signOutButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.signOutButton svg {
  margin-right: 8px;
  flex-shrink: 0;
}

.sidebar.collapsed .signOutButton svg {
  margin-right: 0;
}

.sidebar.collapsed .signOutButton span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.authRecoveryButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px;
  margin-bottom: 8px;
  background-color: rgba(255, 193, 7, 0.8);
  border: none;
  border-radius: 4px;
  color: #212529;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.authRecoveryButton:hover {
  background-color: rgba(255, 193, 7, 1);
}

.authRecoveryButton svg {
  margin-right: 6px;
}

.mainContent {
  display: grid;
  grid-template-rows: auto 1fr;
  background-color: #f5f7fa;
  height: 100vh;
  overflow: hidden;
}

/* Collapsed layout main content (default) */
.mainContent.collapsedLayout {
  /* Grid layout handles positioning */
}

/* Expanded layout main content */
.mainContent.expandedLayout {
  /* Grid layout handles positioning */
}

.header {
  height: 60px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 16px;
  z-index: 10;
  flex-shrink: 0;
}

.menuButton {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 15px;
}

.pageTitle {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.headerActions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.content {
  padding: 12px 16px;
  overflow-y: auto;
  height: calc(100vh - 60px);
}

/* Mobile styles */
@media (max-width: 768px) {
  .adminLayout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    transform: translateX(-100%);
    width: 280px;
    z-index: 1000;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .closeSidebar {
    display: block;
  }

  .mainContent {
    grid-column: 1;
  }

  .menuButton {
    display: block;
  }

  /* Hide collapse toggle on mobile since sidebar overlays */
  .collapseToggle {
    display: none;
  }

  .content {
    height: calc(100vh - 60px);
    padding: 8px 12px;
  }
}
