import { useState, useEffect, useCallback, useRef } from 'react';
import Image from 'next/image';
import { useInView } from 'react-intersection-observer';
import styles from '@/styles/Gallery.module.css';

/**
 * Optimized Gallery Component with lazy loading and browser compatibility
 * Addresses private browsing mode issues and performance concerns
 */
export default function OptimizedGallery({ galleryData = [], activeCategory = 'all' }) {
  const [selectedService, setSelectedService] = useState(null);
  const [imageLoadErrors, setImageLoadErrors] = useState(new Set());
  const [retryAttempts, setRetryAttempts] = useState(new Map());
  const galleryRef = useRef(null);

  // Filter gallery items based on active category
  const filteredGallery = activeCategory === 'all'
    ? galleryData
    : galleryData.filter(item => item.category === activeCategory);

  // Handle image load errors with retry logic
  const handleImageError = useCallback((imageId, imageSrc) => {
    const currentAttempts = retryAttempts.get(imageId) || 0;
    
    if (currentAttempts < 3) {
      // Retry loading the image after a delay
      setTimeout(() => {
        setRetryAttempts(prev => new Map(prev.set(imageId, currentAttempts + 1)));
        // Force re-render by updating a timestamp
        const img = document.querySelector(`[data-image-id="${imageId}"]`);
        if (img) {
          img.src = `${imageSrc}?retry=${currentAttempts + 1}&t=${Date.now()}`;
        }
      }, 1000 * (currentAttempts + 1)); // Exponential backoff
    } else {
      // Mark as permanently failed
      setImageLoadErrors(prev => new Set(prev.add(imageId)));
    }
  }, [retryAttempts]);

  // Handle successful image load
  const handleImageLoad = useCallback((imageId) => {
    setImageLoadErrors(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
    setRetryAttempts(prev => {
      const newMap = new Map(prev);
      newMap.delete(imageId);
      return newMap;
    });
  }, []);

  // Handle clicking on a gallery item to open lightbox
  const handleGalleryItemClick = useCallback((item) => {
    setSelectedService(item);
  }, []);

  // Handle closing the lightbox
  const closeLightbox = useCallback(() => {
    setSelectedService(null);
  }, []);

  // Handle keyboard navigation for lightbox
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!selectedService) return;

      if (e.key === 'Escape') {
        closeLightbox();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedService, closeLightbox]);

  // Preload images for better performance
  useEffect(() => {
    const preloadImages = () => {
      filteredGallery.slice(0, 6).forEach(item => {
        if (item.mainImage) {
          const img = new window.Image();
          img.src = item.mainImage;
        }
      });
    };

    // Preload after component mounts
    const timer = setTimeout(preloadImages, 100);
    return () => clearTimeout(timer);
  }, [filteredGallery]);

  return (
    <div className={styles.galleryContainer} ref={galleryRef}>
      {/* Gallery Grid */}
      <section className={styles.galleryGrid}>
        {filteredGallery.map((item) => (
          <GalleryItem
            key={item.id}
            item={item}
            onImageError={handleImageError}
            onImageLoad={handleImageLoad}
            onClick={handleGalleryItemClick}
            hasError={imageLoadErrors.has(item.id)}
            retryCount={retryAttempts.get(item.id) || 0}
          />
        ))}
      </section>

      {/* Lightbox */}
      {selectedService && (
        <Lightbox
          service={selectedService}
          onClose={closeLightbox}
          onImageError={handleImageError}
          onImageLoad={handleImageLoad}
          hasError={imageLoadErrors.has(`lightbox-${selectedService.id}`)}
        />
      )}
    </div>
  );
}

/**
 * Individual Gallery Item Component with lazy loading
 */
function GalleryItem({ item, onImageError, onImageLoad, onClick, hasError, retryCount }) {
  const { ref, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
    rootMargin: '50px'
  });

  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageLoadSuccess = useCallback(() => {
    setImageLoaded(true);
    onImageLoad(item.id);
  }, [item.id, onImageLoad]);

  const handleImageLoadError = useCallback(() => {
    onImageError(item.id, item.mainImage);
  }, [item.id, item.mainImage, onImageError]);

  return (
    <div
      ref={ref}
      className={styles.galleryItem}
      onClick={() => onClick(item)}
    >
      <div className={styles.galleryImageContainer}>
        {inView && (
          <>
            {!hasError ? (
              <img
                src={`${item.mainImage}${retryCount > 0 ? `?retry=${retryCount}&t=${Date.now()}` : ''}`}
                alt={item.title}
                className={`${styles.galleryImage} ${imageLoaded ? styles.loaded : styles.loading}`}
                onLoad={handleImageLoadSuccess}
                onError={handleImageLoadError}
                data-image-id={item.id}
                loading="lazy"
              />
            ) : (
              <div className={styles.imagePlaceholder}>
                <span>Image unavailable</span>
              </div>
            )}
            
            {!imageLoaded && !hasError && (
              <div className={styles.imageLoader}>
                <div className={styles.spinner}></div>
              </div>
            )}
          </>
        )}
        
        <div className={styles.galleryOverlay}>
          <span className={styles.viewMore}>View Larger</span>
        </div>
      </div>
      <h3 className={styles.galleryItemTitle}>{item.title}</h3>
    </div>
  );
}

/**
 * Lightbox Component with optimized image loading
 */
function Lightbox({ service, onClose, onImageError, onImageLoad, hasError }) {
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleImageLoadSuccess = useCallback(() => {
    setImageLoaded(true);
    onImageLoad(`lightbox-${service.id}`);
  }, [service.id, onImageLoad]);

  const handleImageLoadError = useCallback(() => {
    onImageError(`lightbox-${service.id}`, service.mainImage);
  }, [service.id, service.mainImage, onImageError]);

  return (
    <div className={styles.lightbox} onClick={onClose}>
      <div className={styles.lightboxContent} onClick={(e) => e.stopPropagation()}>
        {!hasError ? (
          <>
            <img
              src={service.mainImage}
              alt={service.title}
              className={`${styles.lightboxImage} ${imageLoaded ? styles.loaded : styles.loading}`}
              onLoad={handleImageLoadSuccess}
              onError={handleImageLoadError}
            />
            {!imageLoaded && (
              <div className={styles.lightboxLoader}>
                <div className={styles.spinner}></div>
              </div>
            )}
          </>
        ) : (
          <div className={styles.lightboxError}>
            <span>Unable to load image</span>
          </div>
        )}
        
        <p className={styles.lightboxCaption}>{service.title}</p>
        <button className={styles.closeButton} onClick={onClose}>
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    </div>
  );
}
