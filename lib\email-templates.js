/**
 * Email Templates for User Management System
 * Provides templates for welcome emails, onboarding, and notifications
 */

/**
 * Generate welcome email for new users
 * @param {Object} user - User object
 * @param {string} user.name - User's name
 * @param {string} user.email - User's email
 * @param {string} user.role - User's role
 * @returns {Object} Email template with subject and HTML body
 */
export function generateWelcomeEmail(user) {
  const { name, email, role } = user
  
  const roleSpecificContent = getRoleSpecificContent(role)
  
  const subject = `Welcome to Ocean Soul Sparkles Team, ${name}!`
  
  const htmlBody = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Welcome to Ocean Soul Sparkles</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8f9fa;
        }
        .container {
          background: white;
          border-radius: 12px;
          padding: 30px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #f0f0f0;
        }
        .logo {
          font-size: 2rem;
          font-weight: bold;
          color: #6e8efb;
          margin-bottom: 10px;
        }
        .welcome-message {
          font-size: 1.2rem;
          color: #333;
          margin-bottom: 20px;
        }
        .role-badge {
          display: inline-block;
          background: linear-gradient(135deg, #6e8efb, #a777e3);
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-weight: 600;
          font-size: 0.9rem;
          margin: 10px 0;
        }
        .content-section {
          margin: 25px 0;
          padding: 20px;
          background: #f8f9ff;
          border-radius: 8px;
          border-left: 4px solid #6e8efb;
        }
        .content-section h3 {
          color: #333;
          margin: 0 0 15px 0;
          font-size: 1.1rem;
        }
        .next-steps {
          background: #e8f5e8;
          border-left-color: #28a745;
        }
        .contact-info {
          background: #fff3cd;
          border-left-color: #ffc107;
        }
        .button {
          display: inline-block;
          background: linear-gradient(135deg, #6e8efb, #a777e3);
          color: white;
          text-decoration: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-weight: 600;
          margin: 15px 0;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 2px solid #f0f0f0;
          color: #666;
          font-size: 0.9rem;
        }
        ul {
          padding-left: 20px;
        }
        li {
          margin: 8px 0;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">🌊 Ocean Soul Sparkles</div>
          <div class="welcome-message">Welcome to the team...</div>
          <div class="role-badge">${roleSpecificContent.roleTitle}</div>
        </div>

        <p>Dear ${name},</p>
        
        <p>We're absolutely thrilled to welcome you to the Ocean Soul Sparkles family! Your journey with us begins now, and we're excited to have someone with your talents and passion join our team.</p>

        ${roleSpecificContent.welcomeContent}

        <div class="content-section next-steps">
          <h3>🚀 Next Steps</h3>
          ${roleSpecificContent.nextSteps}
        </div>

        ${roleSpecificContent.applicationForm ? `
        <div class="content-section">
          <h3>📝 Complete Your Application</h3>
          <p>To finalize your onboarding, please complete our detailed application form:</p>
          <a href="${process.env.NEXT_PUBLIC_SITE_URL}/apply/${role}" class="button">
            Complete Application Form
          </a>
          <p><small>This link will take you to a secure form where you can provide additional details about your experience and preferences.</small></p>
        </div>
        ` : ''}

        <div class="content-section contact-info">
          <h3>📞 Contact Information</h3>
          <p>If you have any questions or need assistance, don't hesitate to reach out:</p>
          <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Phone:</strong> Available in your admin portal</li>
            <li><strong>Admin Portal:</strong> <a href="${process.env.NEXT_PUBLIC_SITE_URL}/admin">Access Here</a></li>
          </ul>
        </div>

        <p>We're committed to supporting your success and growth within our team. Welcome aboard!</p>

        <p>Warm regards,<br>
        <strong>The Ocean Soul Sparkles Team</strong></p>

        <div class="footer">
          <p>Ocean Soul Sparkles - Beauty & Wellness Services</p>
          <p>This email was sent to ${email}</p>
        </div>
      </div>
    </body>
    </html>
  `

  return {
    subject,
    htmlBody,
    textBody: generateTextVersion(user, roleSpecificContent)
  }
}

/**
 * Get role-specific content for welcome emails
 * @param {string} role - User role
 * @returns {Object} Role-specific content
 */
function getRoleSpecificContent(role) {
  const content = {
    dev: {
      roleTitle: 'Developer',
      welcomeContent: `
        <p>As a Developer on our team, you'll have full access to our systems and will play a crucial role in maintaining and enhancing our digital infrastructure. Your technical expertise will help us deliver exceptional experiences to our clients.</p>
      `,
      nextSteps: `
        <ul>
          <li>Access your developer admin panel with full system privileges</li>
          <li>Review our technical documentation and codebase</li>
          <li>Set up your development environment</li>
          <li>Schedule a technical onboarding session with the team</li>
        </ul>
      `,
      applicationForm: false
    },
    admin: {
      roleTitle: 'Administrator',
      welcomeContent: `
        <p>As an Administrator, you'll have comprehensive access to manage our operations, oversee team members, and ensure smooth business operations. Your leadership will be instrumental in our continued success.</p>
      `,
      nextSteps: `
        <ul>
          <li>Access your admin dashboard with full management privileges</li>
          <li>Review current team structure and operations</li>
          <li>Familiarize yourself with our booking and customer management systems</li>
          <li>Schedule a management briefing with the leadership team</li>
        </ul>
      `,
      applicationForm: false
    },
    artist: {
      roleTitle: 'Beauty Artist',
      welcomeContent: `
        <p>Welcome to our talented team of beauty artists! Your creativity and skills will help our clients look and feel their absolute best. We're excited to see the artistry you'll bring to Ocean Soul Sparkles.</p>
      `,
      nextSteps: `
        <ul>
          <li>Complete your detailed artist application form</li>
          <li>Upload your portfolio and certifications</li>
          <li>Schedule your skills assessment and interview</li>
          <li>Set your availability preferences</li>
          <li>Learn about our service offerings and pricing</li>
        </ul>
      `,
      applicationForm: true
    },
    braider: {
      roleTitle: 'Hair Braiding Specialist',
      welcomeContent: `
        <p>Welcome to our skilled team of hair braiding specialists! Your expertise in protective styling and hair artistry will help our clients achieve beautiful, healthy hairstyles. We're thrilled to have you join our braiding family.</p>
      `,
      nextSteps: `
        <ul>
          <li>Complete your detailed braider application form</li>
          <li>Showcase your braiding portfolio and techniques</li>
          <li>Schedule your practical skills demonstration</li>
          <li>Set your availability and service preferences</li>
          <li>Learn about our braiding service menu and techniques</li>
        </ul>
      `,
      applicationForm: true
    },
    user: {
      roleTitle: 'Team Member',
      welcomeContent: `
        <p>Welcome to the Ocean Soul Sparkles community! As a valued team member, you'll have access to our platform and can participate in our growing network of beauty and wellness professionals.</p>
      `,
      nextSteps: `
        <ul>
          <li>Explore your user dashboard</li>
          <li>Update your profile information</li>
          <li>Browse our services and team</li>
          <li>Connect with other team members</li>
        </ul>
      `,
      applicationForm: false
    }
  }

  return content[role] || content.user
}

/**
 * Generate plain text version of welcome email
 * @param {Object} user - User object
 * @param {Object} roleContent - Role-specific content
 * @returns {string} Plain text email body
 */
function generateTextVersion(user, roleContent) {
  return `
Welcome to Ocean Soul Sparkles Team, ${user.name}!

Dear ${user.name},

We're absolutely thrilled to welcome you to the Ocean Soul Sparkles family! Your journey with us begins now, and we're excited to have someone with your talents and passion join our team.

Role: ${roleContent.roleTitle}

${roleContent.welcomeContent.replace(/<[^>]*>/g, '').trim()}

Next Steps:
${roleContent.nextSteps.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim()}

${roleContent.applicationForm ? `
Complete Your Application:
To finalize your onboarding, please complete our detailed application form at:
${process.env.NEXT_PUBLIC_SITE_URL}/apply/${user.role}
` : ''}

Contact Information:
If you have any questions or need assistance, don't hesitate to reach out:
- Email: <EMAIL>
- Admin Portal: ${process.env.NEXT_PUBLIC_SITE_URL}/admin

We're committed to supporting your success and growth within our team. Welcome aboard!

Warm regards,
The Ocean Soul Sparkles Team

---
Ocean Soul Sparkles - Beauty & Wellness Services
This email was sent to ${user.email}
  `.trim()
}

export default {
  generateWelcomeEmail
}
