/**
 * Client Error Logging Endpoint
 * 
 * This endpoint receives client-side authentication errors
 * and logs them server-side for debugging purposes.
 */

export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });
  }

  try {
    const { 
      type, 
      message, 
      timestamp, 
      userAgent, 
      url,
      stack 
    } = req.body;

    // Validate required fields
    if (!type || !message) {
      return res.status(400).json({ 
        error: 'Missing required fields: type and message',
        requestId 
      });
    }

    // Log the client error to server console with clear formatting
    console.log('\n' + '='.repeat(80));
    console.log('🚨 CLIENT-SIDE ERROR DETECTED');
    console.log('='.repeat(80));
    console.log(`Request ID: ${requestId}`);
    console.log(`Timestamp: ${timestamp || new Date().toISOString()}`);
    console.log(`Type: ${type}`);
    console.log(`URL: ${url || 'Unknown'}`);
    console.log(`User Agent: ${userAgent || 'Unknown'}`);
    console.log(`Message: ${message}`);
    
    if (stack) {
      console.log(`Stack Trace:\n${stack}`);
    }
    
    console.log('='.repeat(80) + '\n');

    // Check for specific authentication error patterns
    const isAuthError = message.includes('401') || 
                       message.includes('Unauthorized') || 
                       message.includes('Authentication') ||
                       message.includes('Token') ||
                       message.includes('JWT') ||
                       message.includes('session expired');

    const isReactError130 = message.includes('Objects are not valid as a React child') ||
                           message.includes('Element type is invalid');

    const isGoTrueError = message.includes('Multiple GoTrueClient');

    // Log specific error types with additional context
    if (isAuthError) {
      console.log('⚠️ AUTHENTICATION ERROR DETECTED');
      console.log('This indicates a potential session management issue.');
      console.log('Check token refresh logic and session persistence.\n');
    }

    if (isReactError130) {
      console.log('⚠️ REACT ERROR #130 DETECTED');
      console.log('This indicates unsafe rendering of objects as React children.');
      console.log('Check for components rendering objects directly instead of strings.\n');
    }

    if (isGoTrueError) {
      console.log('⚠️ MULTIPLE GOTRUECLIENT INSTANCES DETECTED');
      console.log('This indicates multiple Supabase client instances.');
      console.log('Check for duplicate imports or client creation.\n');
    }

    // Store error in a simple in-memory log (for development)
    // In production, you might want to store this in a database or external logging service
    if (!global.clientErrorLog) {
      global.clientErrorLog = [];
    }

    global.clientErrorLog.push({
      requestId,
      timestamp: timestamp || new Date().toISOString(),
      type,
      message,
      url,
      userAgent,
      stack,
      isAuthError,
      isReactError130,
      isGoTrueError
    });

    // Keep only the last 100 errors to prevent memory issues
    if (global.clientErrorLog.length > 100) {
      global.clientErrorLog = global.clientErrorLog.slice(-100);
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Error logged successfully',
      requestId,
      errorClassification: {
        isAuthError,
        isReactError130,
        isGoTrueError
      }
    });

  } catch (error) {
    console.error(`[${requestId}] Error processing client error log:`, error);
    
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process client error log',
      requestId
    });
  }
}

/**
 * Get recent client errors (for debugging)
 * This can be called from other diagnostic endpoints
 */
export function getRecentClientErrors(limit = 50) {
  if (!global.clientErrorLog) {
    return [];
  }

  return global.clientErrorLog
    .slice(-limit)
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

/**
 * Clear client error log
 */
export function clearClientErrorLog() {
  global.clientErrorLog = [];
  console.log('Client error log cleared');
}
