<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Service Edit</title>
</head>
<body>
    <h1>Service Edit Test</h1>
    <p>This page will test the service editing functionality.</p>
    
    <script>
        // Test script to check if we can access the admin inventory page
        console.log('Testing service edit functionality...');
        
        // Open the admin inventory page in a new window
        const testWindow = window.open('http://localhost:3001/admin/inventory?tab=services', '_blank');
        
        // Wait for the page to load and then try to click an edit button
        setTimeout(() => {
            console.log('Test window opened. Please manually click on an Edit button to test if React Error #130 is fixed.');
        }, 2000);
    </script>
</body>
</html>
