import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * Find User by Email API
 * Searches across all user-related tables to find user information
 */
export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only administrators and developers can search users.' 
      })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    const { email } = req.query

    if (!email) {
      return res.status(400).json({ error: 'Email parameter is required' })
    }

    const result = {
      email,
      found: false,
      auth_user: null,
      user_role: null,
      user_profile: null,
      applications: [],
      activity_logs: [],
      inconsistencies: []
    }

    // 1. Search in auth.users
    try {
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers()
      if (authError) throw authError

      const authUser = authData.users.find(u => u.email === email)
      if (authUser) {
        result.found = true
        result.auth_user = {
          id: authUser.id,
          email: authUser.email,
          created_at: authUser.created_at,
          last_sign_in_at: authUser.last_sign_in_at,
          email_confirmed_at: authUser.email_confirmed_at,
          user_metadata: authUser.user_metadata
        }

        // 2. Search for user role
        try {
          const { data: roleData, error: roleError } = await adminClient
            .from('user_roles')
            .select('*')
            .eq('id', authUser.id)
            .single()

          if (roleError && roleError.code !== 'PGRST116') {
            throw roleError
          }

          result.user_role = roleData || null
          if (!roleData) {
            result.inconsistencies.push('User exists in auth.users but missing from user_roles')
          }
        } catch (error) {
          result.inconsistencies.push(`Error fetching user role: ${error.message}`)
        }

        // 3. Search for user profile
        try {
          const { data: profileData, error: profileError } = await adminClient
            .from('user_profiles')
            .select('*')
            .eq('id', authUser.id)
            .single()

          if (profileError && profileError.code !== 'PGRST116') {
            throw profileError
          }

          result.user_profile = profileData || null
          if (!profileData) {
            result.inconsistencies.push('User exists in auth.users but missing from user_profiles')
          }
        } catch (error) {
          result.inconsistencies.push(`Error fetching user profile: ${error.message}`)
        }

        // 4. Search for applications
        try {
          const { data: appsData, error: appsError } = await adminClient
            .from('artist_braider_applications')
            .select('*')
            .eq('user_id', authUser.id)

          if (appsError) throw appsError
          result.applications = appsData || []
        } catch (error) {
          result.inconsistencies.push(`Error fetching applications: ${error.message}`)
        }

        // 5. Search for recent activity
        try {
          const { data: activityData, error: activityError } = await adminClient
            .from('user_activity_log')
            .select('*')
            .eq('user_id', authUser.id)
            .order('created_at', { ascending: false })
            .limit(10)

          if (activityError) throw activityError
          result.activity_logs = activityData || []
        } catch (error) {
          result.inconsistencies.push(`Error fetching activity logs: ${error.message}`)
        }
      }
    } catch (error) {
      return res.status(500).json({ 
        error: 'Failed to search auth.users', 
        message: error.message 
      })
    }

    // 6. Search for orphaned records in other tables
    if (!result.found) {
      // Check if there are orphaned records in user_roles
      try {
        const { data: orphanedRoles, error: roleError } = await adminClient
          .from('user_roles')
          .select('*')
          .eq('id', email) // This won't match, but we'll search by email in a different way

        // Since we can't directly search by email in user_roles, we'll note this limitation
        result.inconsistencies.push('Cannot search user_roles by email - requires user ID')
      } catch (error) {
        // Expected to fail, ignore
      }

      // Check if there are orphaned records in user_profiles
      try {
        const { data: orphanedProfiles, error: profileError } = await adminClient
          .from('user_profiles')
          .select('*')
          .eq('id', email) // This won't match, but we'll search by email in a different way

        // Since we can't directly search by email in user_profiles, we'll note this limitation
        result.inconsistencies.push('Cannot search user_profiles by email - requires user ID')
      } catch (error) {
        // Expected to fail, ignore
      }
    }

    // 7. Generate recommendations
    const recommendations = []
    if (result.found) {
      if (result.inconsistencies.length > 0) {
        recommendations.push('Fix data inconsistencies using the cleanup API')
      }
      if (!result.user_role) {
        recommendations.push('Create missing user role record')
      }
      if (!result.user_profile) {
        recommendations.push('Create missing user profile record')
      }
      if (result.inconsistencies.length === 0) {
        recommendations.push('User data is consistent across all tables')
      }
    } else {
      recommendations.push('User not found in auth.users - safe to create new user')
    }

    result.recommendations = recommendations

    return res.status(200).json(result)

  } catch (error) {
    console.error('Error in find user by email:', error)
    return res.status(500).json({ 
      error: 'Search failed', 
      message: error.message 
    })
  }
}
