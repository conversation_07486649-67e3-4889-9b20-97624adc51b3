.dashboard {
  display: grid;
  grid-template-rows: auto auto 1fr;
  gap: 12px;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  overflow: hidden;
}

.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  flex-shrink: 0;
}

.dashboardTitle {
  display: flex;
  flex-direction: column;
}

.dashboardTitle h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.lastRefreshed {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

.dashboardActions {
  display: flex;
  gap: 10px;
}

.refreshButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background-color: #5a0b8d;
}

.refreshButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 16px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(106, 13, 173, 0.1);
  border-radius: 50%;
  border-top-color: #6a0dad;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #ffebee;
  color: #d32f2f;
  padding: 30px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  height: 300px;
}

.errorIcon {
  width: 60px;
  height: 60px;
  color: #d32f2f;
  margin-bottom: 15px;
}

.error h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 10px 0;
}

.error p {
  font-size: 14px;
  margin: 0 0 20px 0;
  max-width: 400px;
}

.retryButton {
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background-color: #b71c1c;
}

.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  flex-shrink: 0;
}

.summaryCard {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 14px;
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.summaryIcon {
  width: 40px;
  height: 40px;
  background-color: #f0f4ff;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  color: #6a0dad;
  flex-shrink: 0;
}

.summaryContent {
  flex: 1;
}

.summaryContent h3 {
  font-size: 12px;
  font-weight: 500;
  color: #666;
  margin: 0 0 3px 0;
}

.summaryValue {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 12px;
  overflow: hidden;
  min-height: 0;
}

.dashboardSection {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 14px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.recentBookings {
  overflow: auto;
  flex: 1;
  min-height: 0;
}

.bookingsTable {
  width: 100%;
  border-collapse: collapse;
}

.bookingsTable th,
.bookingsTable td {
  padding: 8px 10px;
  text-align: left;
}

.bookingsTable th {
  background-color: #f9f9f9;
  font-weight: 500;
  color: #666;
  font-size: 12px;
  position: sticky;
  top: 0;
}

.bookingsTable tr {
  border-bottom: 1px solid #eee;
}

.bookingsTable tr:last-child {
  border-bottom: none;
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.confirmed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.pending {
  background-color: #fff8e1;
  color: #f57c00;
}

.canceled {
  background-color: #ffebee;
  color: #d32f2f;
}

.noData {
  padding: 30px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

@media (max-width: 992px) {
  .dashboardGrid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    gap: 8px;
  }

  .summaryCards {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
  }

  .summaryCard {
    padding: 10px;
  }

  .summaryIcon {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .summaryValue {
    font-size: 16px;
  }

  .dashboardSection {
    padding: 10px;
  }

  .bookingsTable th,
  .bookingsTable td {
    padding: 6px 8px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .summaryCards {
    grid-template-columns: 1fr 1fr;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* New styles for enhanced dashboard */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.title {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.userEmail {
  font-size: 0.9rem;
  color: #6c757d;
}

.logoutButton {
  padding: 8px 16px;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.logoutButton:hover {
  background-color: #e9ecef;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 24px;
}

.tabButton {
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-size: 1rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
}

.tabButton:hover {
  color: #6a0dad;
}

.tabButton.active {
  color: #6a0dad;
  border-bottom-color: #6a0dad;
  font-weight: 500;
}

.tabContent {
  min-height: 600px;
}

.tabHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tabTitle {
  font-size: 1.4rem;
  color: #333;
  margin: 0;
}

.tabActions {
  display: flex;
  gap: 12px;
}

.newButton,
.viewButton {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.newButton {
  background-color: #6a0dad;
  color: white;
  border: none;
}

.newButton:hover {
  background-color: #5a0b9d;
}

.viewButton {
  background-color: #6a0dad;
  color: white;
  border: none;
  text-align: center;
}

.viewButton:hover {
  background-color: #5a0b9d;
}

.calendarContainer {
  margin-bottom: 24px;
}

.selectedBookingInfo {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-top: 24px;
}

.selectedBookingInfo h3 {
  font-size: 1.1rem;
  color: #333;
  margin-top: 0;
  margin-bottom: 12px;
}

.selectedBookingInfo p {
  margin: 8px 0;
  font-size: 0.95rem;
  color: #495057;
}

.bookingActions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #6c757d;
}

.authContainer {
  max-width: 500px;
  margin: 100px auto;
  padding: 40px;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.authContainer h1 {
  font-size: 1.8rem;
  color: #333;
  margin-top: 0;
  margin-bottom: 16px;
}

.authContainer p {
  margin-bottom: 24px;
  color: #6c757d;
}

.loginButton {
  display: inline-block;
  padding: 10px 20px;
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.loginButton:hover {
  background-color: #5a0b9d;
}

/* Responsive styles for enhanced dashboard */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .userInfo {
    width: 100%;
    justify-content: space-between;
  }

  .tabs {
    overflow-x: auto;
    white-space: nowrap;
    padding-bottom: 4px;
  }

  .tabButton {
    padding: 10px 16px;
    font-size: 0.95rem;
  }

  .tabHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .tabActions {
    width: 100%;
    justify-content: space-between;
  }

  .newButton,
  .refreshButton,
  .viewButton {
    flex: 1;
    text-align: center;
  }
}
