import { getAdminClient } from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * API endpoint for customer tags management
 * GET /api/admin/customer-tags - Get all customer tags
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer Tags API called: ${req.method} ${req.url}`);

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req);
    
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ 
        error: 'Authentication failed',
        message: authResult.error?.message || 'Access denied'
      });
    }

    console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}, Role: ${authResult.role}`);

    if (req.method === 'GET') {
      console.log(`[${requestId}] Processing GET request for customer tags`);

      try {
        // Get admin client for database operations
        const adminClient = getAdminClient();
        
        // Fetch customer tags
        const { data: tags, error } = await adminClient
          .from('customer_tags')
          .select('*')
          .order('name');

        if (error) {
          console.error(`[${requestId}] Database error:`, error);
          return res.status(500).json({
            error: 'Database error',
            message: 'Failed to fetch customer tags'
          });
        }

        console.log(`[${requestId}] Successfully fetched ${tags?.length || 0} customer tags`);

        // Set appropriate cache headers for semi-static data
        setCacheHeaders(res, 'customer-tags', 'GET', true, req.query);

        return res.status(200).json({
          success: true,
          tags: tags || [],
          count: tags?.length || 0,
          requestId,
          timestamp: new Date().toISOString()
        });

      } catch (dbError) {
        console.error(`[${requestId}] Database operation failed:`, dbError);
        return res.status(500).json({
          error: 'Database operation failed',
          message: 'Unable to fetch customer tags'
        });
      }
    } else {
      // Method not allowed
      res.setHeader('Allow', ['GET']);
      return res.status(405).json({
        error: 'Method not allowed',
        message: `Method ${req.method} not allowed`
      });
    }

  } catch (error) {
    console.error(`[${requestId}] Customer Tags API Error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: 'An unexpected error occurred'
    });
  }
}
