<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Service Edit Click</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Service Edit Click Test</h1>
        <p>This page will test clicking the Edit button on services to check for React Error #130.</p>
        
        <div>
            <button class="test-button" onclick="openAdminPage()">1. Open Admin Inventory</button>
            <button class="test-button" onclick="testEditClick()">2. Test Edit Click</button>
            <button class="test-button" onclick="checkConsoleErrors()">3. Check Console Errors</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="log" class="log">Test log will appear here...\n</div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let adminWindow = null;
        let errorCount = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            logElement.innerHTML = 'Log cleared...\n';
            errorCount = 0;
        }
        
        function openAdminPage() {
            log('Opening admin inventory page...', 'info');
            adminWindow = window.open('http://localhost:3001/admin/inventory?tab=services', 'adminWindow');
            
            if (adminWindow) {
                log('✅ Admin window opened successfully', 'success');
                
                // Wait for page to load
                setTimeout(() => {
                    log('Waiting for page to load...', 'info');
                    
                    // Try to access the admin window and check if it loaded
                    try {
                        if (adminWindow.document && adminWindow.document.readyState === 'complete') {
                            log('✅ Admin page loaded successfully', 'success');
                        } else {
                            log('⏳ Admin page still loading...', 'warning');
                        }
                    } catch (e) {
                        log('⚠️ Cannot access admin window (cross-origin)', 'warning');
                    }
                }, 3000);
            } else {
                log('❌ Failed to open admin window (popup blocked?)', 'error');
            }
        }
        
        function testEditClick() {
            if (!adminWindow || adminWindow.closed) {
                log('❌ Admin window not open. Please open it first.', 'error');
                return;
            }
            
            log('Testing edit button click...', 'info');
            
            try {
                // Try to find and click an edit button
                const editButtons = adminWindow.document.querySelectorAll('button');
                let editButton = null;
                
                for (let button of editButtons) {
                    if (button.textContent.includes('Edit') || button.textContent.includes('✏️')) {
                        editButton = button;
                        break;
                    }
                }
                
                if (editButton) {
                    log(`✅ Found edit button: "${editButton.textContent}"`, 'success');
                    
                    // Monitor for errors before clicking
                    const originalError = adminWindow.console.error;
                    let reactError = null;
                    
                    adminWindow.console.error = function(...args) {
                        const message = args.join(' ');
                        if (message.includes('Element type is invalid') || 
                            message.includes('Objects are not valid as a React child') ||
                            message.includes('React Error #130')) {
                            reactError = message;
                        }
                        originalError.apply(adminWindow.console, args);
                    };
                    
                    // Click the button
                    editButton.click();
                    log('🖱️ Edit button clicked', 'info');
                    
                    // Check for errors after a delay
                    setTimeout(() => {
                        if (reactError) {
                            log(`❌ React Error #130 detected: ${reactError}`, 'error');
                            errorCount++;
                        } else {
                            log('✅ No React Error #130 detected after click', 'success');
                        }
                        
                        // Restore original console.error
                        adminWindow.console.error = originalError;
                        
                        // Check if modal opened
                        const modals = adminWindow.document.querySelectorAll('[class*="modal"]');
                        if (modals.length > 0) {
                            log(`✅ Modal opened successfully (${modals.length} modal(s) found)`, 'success');
                        } else {
                            log('⚠️ No modal detected after click', 'warning');
                        }
                    }, 1000);
                    
                } else {
                    log('❌ No edit button found on the page', 'error');
                }
                
            } catch (error) {
                log(`❌ Error during test: ${error.message}`, 'error');
            }
        }
        
        function checkConsoleErrors() {
            log('Checking for console errors...', 'info');
            
            if (!adminWindow || adminWindow.closed) {
                log('❌ Admin window not open', 'error');
                return;
            }
            
            try {
                // Override console.error to capture errors
                const originalError = adminWindow.console.error;
                let capturedErrors = [];
                
                adminWindow.console.error = function(...args) {
                    const message = args.join(' ');
                    capturedErrors.push(message);
                    originalError.apply(adminWindow.console, args);
                };
                
                // Monitor for 5 seconds
                setTimeout(() => {
                    adminWindow.console.error = originalError;
                    
                    if (capturedErrors.length === 0) {
                        log('✅ No console errors detected', 'success');
                    } else {
                        log(`⚠️ ${capturedErrors.length} console error(s) detected:`, 'warning');
                        capturedErrors.forEach((error, index) => {
                            log(`  ${index + 1}. ${error}`, 'error');
                        });
                    }
                }, 5000);
                
                log('🔍 Monitoring console errors for 5 seconds...', 'info');
                
            } catch (error) {
                log(`❌ Error checking console: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        log('🚀 Service Edit Click Test initialized', 'info');
        log('Instructions:', 'info');
        log('1. Click "Open Admin Inventory" to open the admin page', 'info');
        log('2. Click "Test Edit Click" to simulate clicking an Edit button', 'info');
        log('3. Click "Check Console Errors" to monitor for React errors', 'info');
    </script>
</body>
</html>
