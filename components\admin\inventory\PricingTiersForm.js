import { useState, useEffect } from 'react';
import styles from '@/styles/admin/PricingTiersForm.module.css';

export default function PricingTiersForm({ pricingTiers = [], onChange }) {
  const [tiers, setTiers] = useState([]);
  const [lastPricingTiersLength, setLastPricingTiersLength] = useState(0);

  // Initialize tiers from props - allow re-initialization when pricingTiers changes
  useEffect(() => {
    console.log('🔍 PricingTiersForm - useEffect called', {
      pricingTiersLength: pricingTiers?.length,
      lastPricingTiersLength,
      pricingTiers
    });

    // Only initialize if we have new pricing tiers data or if we haven't initialized yet
    const shouldInitialize = (
      (pricingTiers && pricingTiers.length > 0 && pricingTiers.length !== lastPricingTiersLength) ||
      (tiers.length === 0 && pricingTiers?.length === 0)
    );

    if (shouldInitialize) {
      console.log('🔍 PricingTiersForm - Initializing tiers');

      if (pricingTiers && pricingTiers.length > 0) {
        console.log('🔍 PricingTiersForm - Processing existing pricing tiers:', pricingTiers.length);
        const processedTiers = pricingTiers.map(tier => ({
          id: tier.id || null,
          name: String(tier.name || ''),
          description: String(tier.description || ''),
          duration: String(tier.duration || ''),
          price: String(tier.price || ''),
          is_default: Boolean(tier.is_default),
          sort_order: Number(tier.sort_order || 0)
        }));
        console.log('🔍 PricingTiersForm - Processed tiers:', processedTiers);
        setTiers(processedTiers);
        setLastPricingTiersLength(pricingTiers.length);
      } else if (tiers.length === 0) {
        console.log('🔍 PricingTiersForm - Creating default tier');
        // Create a default tier only if we have no tiers at all
        setTiers([{
          id: null,
          name: 'Standard',
          description: 'Standard service duration and pricing',
          duration: '60',
          price: '50',
          is_default: true,
          sort_order: 1
        }]);
        setLastPricingTiersLength(0);
      }
    }
  }, [pricingTiers, lastPricingTiersLength, tiers.length]);

  // Notify parent of changes only when user makes changes (not on initialization)
  const notifyParent = (newTiers) => {
    if (onChange) {
      onChange(newTiers);
    }
  };

  // Add a new pricing tier
  const addTier = () => {
    const newTier = {
      id: null,
      name: '',
      description: '',
      duration: '60',
      price: '50',
      is_default: false,
      sort_order: tiers.length + 1
    };
    const newTiers = [...tiers, newTier];
    setTiers(newTiers);
    notifyParent(newTiers);
  };

  // Remove a pricing tier
  const removeTier = (index) => {
    if (tiers.length <= 1) {
      alert('At least one pricing tier is required.');
      return;
    }

    const newTiers = tiers.filter((_, i) => i !== index);

    // If we removed the default tier, make the first tier default
    if (tiers[index].is_default && newTiers.length > 0) {
      newTiers[0].is_default = true;
    }

    setTiers(newTiers);
    notifyParent(newTiers);
  };

  // Update a specific tier
  const updateTier = (index, field, value) => {
    const newTiers = [...tiers];

    if (field === 'is_default' && value) {
      // Only one tier can be default
      newTiers.forEach((tier, i) => {
        tier.is_default = i === index;
      });
    } else {
      newTiers[index][field] = value;
    }

    setTiers(newTiers);
    notifyParent(newTiers);
  };

  // Move tier up/down
  const moveTier = (index, direction) => {
    const newTiers = [...tiers];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;

    if (targetIndex < 0 || targetIndex >= newTiers.length) return;

    // Swap tiers
    [newTiers[index], newTiers[targetIndex]] = [newTiers[targetIndex], newTiers[index]];

    // Update sort orders
    newTiers.forEach((tier, i) => {
      tier.sort_order = i + 1;
    });

    setTiers(newTiers);
    notifyParent(newTiers);
  };

  return (
    <div className={styles.pricingTiersForm}>
      <div className={styles.header}>
        <h3>Service Pricing Tiers</h3>
        <p className={styles.description}>
          Create multiple pricing options for this service with different durations and prices.
        </p>
      </div>

      <div className={styles.tiersContainer}>
        {tiers.map((tier, index) => (
          <div key={index} className={styles.tierCard}>
            <div className={styles.tierHeader}>
              <div className={styles.tierControls}>
                <button
                  type="button"
                  onClick={() => moveTier(index, 'up')}
                  disabled={index === 0}
                  className={styles.moveButton}
                  title="Move up"
                >
                  ↑
                </button>
                <button
                  type="button"
                  onClick={() => moveTier(index, 'down')}
                  disabled={index === tiers.length - 1}
                  className={styles.moveButton}
                  title="Move down"
                >
                  ↓
                </button>
                <span className={styles.tierNumber}>#{index + 1}</span>
              </div>
              
              <button
                type="button"
                onClick={() => removeTier(index)}
                className={styles.removeButton}
                disabled={tiers.length <= 1}
                title="Remove tier"
              >
                ✕
              </button>
            </div>

            <div className={styles.tierFields}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>
                    Tier Name <span className={styles.required}>*</span>
                  </label>
                  <input
                    type="text"
                    value={tier.name}
                    onChange={(e) => updateTier(index, 'name', e.target.value)}
                    placeholder="e.g., Basic, Standard, Premium"
                    className={styles.input}
                    required
                  />
                </div>

                <div className={styles.formGroup}>
                  <label>
                    Duration (minutes) <span className={styles.required}>*</span>
                  </label>
                  <input
                    type="number"
                    value={tier.duration}
                    onChange={(e) => updateTier(index, 'duration', e.target.value)}
                    placeholder="60"
                    min="1"
                    className={styles.input}
                    required
                  />
                </div>

                <div className={styles.formGroup}>
                  <label>
                    Price (AUD) <span className={styles.required}>*</span>
                  </label>
                  <input
                    type="number"
                    value={tier.price}
                    onChange={(e) => updateTier(index, 'price', e.target.value)}
                    placeholder="50.00"
                    step="0.01"
                    min="0"
                    className={styles.input}
                    required
                  />
                </div>
              </div>

              <div className={styles.formGroup}>
                <label>Description</label>
                <textarea
                  value={tier.description}
                  onChange={(e) => updateTier(index, 'description', e.target.value)}
                  placeholder="Describe what's included in this tier..."
                  className={styles.textarea}
                  rows={2}
                />
              </div>

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={tier.is_default}
                    onChange={(e) => updateTier(index, 'is_default', e.target.checked)}
                  />
                  Default tier (shown first on booking page)
                </label>
              </div>
            </div>
          </div>
        ))}
      </div>

      <button
        type="button"
        onClick={addTier}
        className={styles.addTierButton}
      >
        + Add Pricing Tier
      </button>

      <div className={styles.helpText}>
        <p>💡 <strong>Tips:</strong></p>
        <ul>
          <li>Create different tiers for various service durations (e.g., Basic 30min, Standard 60min, Premium 90min)</li>
          <li>The default tier will be highlighted on the booking page</li>
          <li>Use descriptive names that customers will understand</li>
          <li>At least one pricing tier is required</li>
        </ul>
      </div>
    </div>
  );
}
