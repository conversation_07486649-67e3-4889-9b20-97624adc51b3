/* QR Code Landing Page - Mobile-First Design */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #333;
  overflow-x: hidden;
}

/* Header Section */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo img {
  height: 40px;
  width: auto;
}

.eventBadge {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Event Information */
.eventInfo {
  background: white;
  margin: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.eventTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  line-height: 1.2;
}

.eventDetails {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.eventDetail {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.detailIcon {
  font-size: 1.2rem;
  min-width: 24px;
}

.detailText {
  font-size: 0.9rem;
  color: #555;
  font-weight: 500;
}

/* Services Preview */
.servicesPreview {
  background: white;
  margin: 1rem;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.servicesTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  text-align: center;
}

.servicesList {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.serviceItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: transform 0.2s ease;
}

.serviceItem:active {
  transform: scale(0.98);
}

.serviceIcon {
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 0.2rem 0;
}

.servicePrice {
  font-size: 0.9rem;
  color: #4ECDC4;
  font-weight: 600;
  margin: 0;
}

.moreServices {
  text-align: center;
  padding: 1rem;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

/* Booking Information */
.bookingInfo {
  margin: 1rem;
}

.infoCard {
  background: white;
  padding: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.infoTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  text-align: center;
}

.infoList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8rem;
}

.infoList li {
  font-size: 0.9rem;
  color: #555;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Action Buttons */
.actions {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.bookButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 1.2rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(78, 205, 196, 0.3);
  min-height: 56px; /* Touch-friendly size */
}

.bookButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(78, 205, 196, 0.4);
}

.bookButton:active {
  transform: translateY(0);
}

.bookButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.buttonIcon {
  font-size: 1.2rem;
}

.loadingSpinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.alternativeActions {
  display: flex;
  gap: 1rem;
}

.linkButton {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  color: #4ECDC4;
  border: 2px solid #4ECDC4;
  padding: 1rem;
  border-radius: 12px;
  text-decoration: none;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
  min-height: 48px; /* Touch-friendly size */
  display: flex;
  align-items: center;
  justify-content: center;
}

.linkButton:hover {
  background: #4ECDC4;
  color: white;
}

/* Footer */
.footer {
  text-align: center;
  padding: 2rem 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.footerText {
  font-size: 0.9rem;
  margin: 0 0 0.5rem 0;
}

.securityText {
  font-size: 0.8rem;
  margin: 0;
  opacity: 0.7;
}

/* Error States */
.errorContainer {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  text-align: center;
  background: white;
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #e74c3c;
  margin: 0 0 1rem 0;
}

.errorMessage {
  font-size: 1rem;
  color: #666;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

.expiredInfo,
.notStartedInfo {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.expiredInfo p,
.notStartedInfo p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.alternativeButton {
  background: #4ECDC4;
  color: white;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  margin: 0.5rem;
  display: inline-block;
  transition: all 0.3s ease;
  min-width: 140px;
}

.alternativeButton:hover {
  background: #44A08D;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 480px) {
  .eventTitle {
    font-size: 1.5rem;
  }
  
  .infoList {
    grid-template-columns: 1fr;
  }
  
  .alternativeActions {
    flex-direction: column;
  }
  
  .linkButton {
    margin-bottom: 0.5rem;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 480px;
    margin: 0 auto;
    box-shadow: 0 0 40px rgba(0, 0, 0, 0.1);
  }
}

/* Touch device optimizations */
@media (hover: none) {
  .bookButton:hover {
    transform: none;
  }
  
  .linkButton:hover {
    background: rgba(255, 255, 255, 0.9);
    color: #4ECDC4;
  }
  
  .serviceItem:hover {
    transform: none;
  }
}
