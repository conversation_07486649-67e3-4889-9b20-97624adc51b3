/* Event Expense Tracker Styles */

.container {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
}

.headerInfo h3 {
  margin: 0 0 0.5rem 0;
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
}

.totalExpenses {
  font-size: 1.1rem;
  font-weight: 600;
  color: #059669;
}

.addButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

/* Summary Cards */
.summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.summaryCard {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.summaryLabel {
  font-size: 0.875rem;
  color: #64748b;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.summaryValue {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.overBudget {
  color: #dc2626 !important;
}

.underBudget {
  color: #059669 !important;
}

/* Category Breakdown */
.categoryBreakdown {
  margin-bottom: 2rem;
}

.categoryBreakdown h4 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.categoryList {
  display: grid;
  gap: 0.5rem;
}

.categoryItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 6px;
  border-left: 4px solid #4ECDC4;
}

.categoryName {
  font-weight: 500;
  color: #374151;
}

.categoryAmount {
  font-weight: 600;
  color: #059669;
}

/* Expenses List */
.expensesList {
  display: grid;
  gap: 1rem;
}

.emptyState {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.emptyState p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.addFirstButton {
  background: linear-gradient(45deg, #6366f1, #8b5cf6);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.addFirstButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.expenseItem {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem;
  background: #ffffff;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.expenseItem:hover {
  border-color: #4ECDC4;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.15);
}

.expenseInfo {
  flex: 1;
}

.expenseName {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.expenseDetails {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.expenseCategory {
  background: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
}

.expenseDate {
  color: #6b7280;
  font-size: 0.875rem;
}

.expenseVendor {
  color: #059669;
  font-size: 0.875rem;
  font-weight: 500;
}

.expenseDescription {
  color: #6b7280;
  font-size: 0.875rem;
  line-height: 1.4;
}

.expenseActions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.75rem;
}

.expenseAmount {
  font-size: 1.25rem;
  font-weight: 700;
  color: #dc2626;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.editButton, .deleteButton {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.editButton {
  background: #f3f4f6;
  color: #374151;
}

.editButton:hover {
  background: #e5e7eb;
}

.deleteButton {
  background: #fee2e2;
  color: #dc2626;
}

.deleteButton:hover {
  background: #fecaca;
}

/* Form Styles */
.expenseForm {
  display: grid;
  gap: 1rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.input, .textarea {
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.input:focus, .textarea:focus {
  outline: none;
  border-color: #4ECDC4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 80px;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  margin-top: 1.5rem;
}

.checkbox {
  width: 1rem;
  height: 1rem;
  accent-color: #4ECDC4;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 2px solid #e5e7eb;
}

.cancelButton, .submitButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelButton {
  background: #f3f4f6;
  color: #374151;
}

.cancelButton:hover {
  background: #e5e7eb;
}

.submitButton {
  background: linear-gradient(45deg, #4ECDC4, #44A08D);
  color: white;
  box-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
}

.submitButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.submitButton:disabled, .cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .summary {
    grid-template-columns: 1fr;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .expenseItem {
    flex-direction: column;
    gap: 1rem;
  }

  .expenseActions {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .formActions {
    flex-direction: column;
  }

  .cancelButton, .submitButton {
    width: 100%;
  }
}
