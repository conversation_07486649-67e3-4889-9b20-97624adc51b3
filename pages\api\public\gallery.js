/**
 * Public Gallery API
 * Provides gallery data for the frontend gallery page
 */

import { getAdminClient } from '@/lib/supabase';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      category,
      featured,
      limit = 50,
      offset = 0,
      include_categories = false
    } = req.query;

    const supabaseAdmin = getAdminClient();

    // Build query for gallery items
    let query = supabaseAdmin
      .from('gallery_items')
      .select(`
        id,
        title,
        description,
        category,
        main_image_url,
        gallery_images,
        featured,
        display_order,
        meta_title,
        meta_description,
        created_at
      `)
      .eq('status', 'active'); // Only return active items

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('category', category);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured === 'true');
    }

    // Apply sorting (featured items first, then by display order, then by creation date)
    query = query.order('featured', { ascending: false })
                 .order('display_order', { ascending: true })
                 .order('created_at', { ascending: false });

    // Apply pagination
    query = query.range(parseInt(offset), parseInt(offset) + parseInt(limit) - 1);

    const { data: galleryItems, error } = await query;

    if (error) {
      console.error('Error fetching gallery items:', error);
      return res.status(500).json({ error: 'Failed to fetch gallery items' });
    }

    // Transform data to match the expected frontend format
    const transformedItems = (galleryItems || []).map(item => ({
      id: item.id,
      title: item.title || '',
      description: item.description || '',
      category: item.category || 'all',
      mainImage: item.main_image_url || '',
      images: item.gallery_images || [],
      featured: item.featured || false,
      displayOrder: item.display_order || 0,
      metaTitle: item.meta_title || item.title || '',
      metaDescription: item.meta_description || item.description || '',
      createdAt: item.created_at
    }));

    let responseData = {
      success: true,
      data: transformedItems,
      pagination: {
        total: transformedItems.length,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: transformedItems.length === parseInt(limit)
      }
    };

    // Optionally include categories
    if (include_categories === 'true') {
      const { data: categories, error: categoriesError } = await supabaseAdmin
        .from('gallery_categories')
        .select('*')
        .eq('status', 'active')
        .order('display_order', { ascending: true });

      if (!categoriesError) {
        responseData.categories = (categories || []).map(cat => ({
          id: cat.id,
          name: cat.name || '',
          slug: cat.slug || '',
          description: cat.description || '',
          color: cat.color || '#6a0dad',
          displayOrder: cat.display_order || 0
        }));
      }
    }

    return res.status(200).json(responseData);
  } catch (error) {
    console.error('Gallery API error:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to fetch gallery items',
      message: 'Database error occurred'
    });
  }
}
