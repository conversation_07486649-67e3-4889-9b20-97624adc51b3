.settingsForm {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
}

.formSection {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.formSection:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.formSection h2 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-top: 0;
  margin-bottom: 1rem;
}

.formGroup {
  margin-bottom: 1rem;
  width: 100%;
}

.formRow {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.formRow .formGroup {
  flex: 1;
  min-width: 200px;
}

.formGroup label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.formGroup input[type="text"],
.formGroup input[type="email"],
.formGroup input[type="tel"],
.formGroup input[type="number"],
.formGroup input[type="url"],
.formGroup textarea,
.formGroup select {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input[type="text"]:focus,
.formGroup input[type="email"]:focus,
.formGroup input[type="tel"]:focus,
.formGroup input[type="number"]:focus,
.formGroup input[type="url"]:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.formGroup small {
  display: block;
  font-size: 0.75rem;
  color: #718096;
  margin-top: 0.375rem;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
}

.colorPickerWrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.colorPickerWrapper input[type="color"] {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  cursor: pointer;
}

.colorPickerWrapper input[type="text"] {
  flex: 1;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
}

.saveButton {
  background-color: #3788d8;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover {
  background-color: #2c6cb0;
}

.saveButton:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

/* Settings Container */
.settingsContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
  overflow-x: auto;
  flex-shrink: 0;
}

.tab {
  background: none;
  border: none;
  padding: 0.75rem 1.2rem;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: 100px;
  text-align: center;
}

.tab:hover {
  background-color: #e9ecef;
  color: #2c3e50;
}

.tab.active {
  color: #3788d8;
  border-bottom-color: #3788d8;
  background-color: #fff;
  font-weight: 600;
}

/* Tab Content */
.tabContent {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

/* Connection Testing */
.connectionTest {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.375rem;
}

.testButton {
  background-color: #28a745;
  color: #fff;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.testButton:hover {
  background-color: #218838;
}

.testButton:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Connection Status Indicators */
.statusTesting {
  color: #ffc107;
  font-weight: 500;
  font-size: 0.875rem;
}

.statusSuccess {
  color: #28a745;
  font-weight: 500;
  font-size: 0.875rem;
}

.statusError {
  color: #dc3545;
  font-weight: 500;
  font-size: 0.875rem;
}

/* Code Textarea */
.codeTextarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8rem;
  line-height: 1.4;
  background-color: #f8f9fa;
  border: 1px solid #e1e5e9;
}

/* Password Input */
.formGroup input[type="password"] {
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s;
}

.formGroup input[type="password"]:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .tabNavigation {
    overflow-x: auto;
    scrollbar-width: thin;
  }

  .tab {
    min-width: 80px;
    padding: 0.6rem 1rem;
    font-size: 0.75rem;
  }

  .tabContent {
    padding: 1rem;
  }

  .formSection {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
  }

  .formSection h2 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .formGroup {
    margin-bottom: 0.75rem;
  }

  .formRow {
    flex-direction: column;
    gap: 0.75rem;
  }

  .formRow .formGroup {
    min-width: auto;
  }

  .connectionTest {
    flex-direction: column;
    align-items: flex-start;
    padding: 0.75rem;
  }

  .formActions {
    margin-top: 1.5rem;
  }
}

@media (max-width: 480px) {
  .tab {
    min-width: 70px;
    padding: 0.5rem 0.75rem;
    font-size: 0.7rem;
  }

  .tabContent {
    padding: 0.75rem;
  }

  .formSection h2 {
    font-size: 0.95rem;
  }

  .formGroup input,
  .formGroup textarea,
  .formGroup select {
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  .connectionTest {
    padding: 0.5rem;
  }
}
