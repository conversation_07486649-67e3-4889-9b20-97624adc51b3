.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.2s ease-out;
}

.modalContainer {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  max-height: 95vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
  margin: 8px;
}

.modalSmall {
  width: calc(100% - 16px);
  max-width: 480px;
}

.modalMedium {
  width: calc(100% - 16px);
  max-width: 760px;
}

.modalLarge {
  width: calc(100% - 16px);
  max-width: 1140px;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}

.modalTitle {
  font-size: 1.3rem;
  color: #333;
  margin: 0;
}

.modalContent {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
}

.closeButton,
.closeButtonCorner {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.closeButton:hover,
.closeButtonCorner:hover {
  color: #333;
}

.closeButtonCorner {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .modalContainer {
    max-height: 98vh;
    margin: 4px;
  }

  .modalSmall,
  .modalMedium,
  .modalLarge {
    width: calc(100% - 8px);
  }

  .modalHeader {
    padding: 10px 12px;
  }

  .modalTitle {
    font-size: 1.1rem;
  }

  .modalContent {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .modalContainer {
    max-height: 99vh;
    margin: 2px;
  }

  .modalSmall,
  .modalMedium,
  .modalLarge {
    width: calc(100% - 4px);
  }

  .modalHeader {
    padding: 8px 10px;
  }

  .modalTitle {
    font-size: 1rem;
  }

  .modalContent {
    padding: 10px;
  }
}
