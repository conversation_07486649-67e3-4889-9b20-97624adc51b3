import { authenticateAdminRequest } from '@/lib/admin-auth';
import { getQRCodeAnalytics } from '@/lib/qr-code-manager';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for QR code analytics and revenue tracking
 * Provides detailed analytics for individual QR codes
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] QR Code Analytics API called: ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    const { qrCodeId } = req.query;

    console.log(`[${requestId}] Authenticated user: ${user.email} (${role}) for QR code: ${qrCodeId}`);

    if (req.method === 'GET') {
      return await handleGetAnalytics(req, res, qrCodeId, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }

  } catch (error) {
    console.error(`[${requestId}] Error in QR code analytics API:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message 
    });
  }
}

/**
 * Handle GET request - fetch comprehensive analytics for QR code
 */
async function handleGetAnalytics(req, res, qrCodeId, requestId) {
  try {
    const { 
      date_range = '30',
      include_bookings = false,
      include_revenue_breakdown = false 
    } = req.query;

    console.log(`[${requestId}] Fetching analytics for QR code: ${qrCodeId}`);

    // Get basic QR code analytics
    const analyticsResult = await getQRCodeAnalytics(qrCodeId);
    
    if (!analyticsResult.success) {
      return res.status(404).json({ 
        error: 'QR code not found or analytics unavailable',
        message: analyticsResult.error 
      });
    }

    const analytics = analyticsResult.analytics;

    // Calculate date range for detailed queries
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(date_range));

    // Get booking details if requested
    let bookingDetails = null;
    if (include_bookings === 'true') {
      const { data: bookings, error: bookingsError } = await supabase
        .from('event_bookings')
        .select(`
          id,
          revenue_amount,
          artist_earnings,
          platform_earnings,
          payment_method,
          payment_status,
          created_at,
          customers:customer_id (
            name,
            email
          ),
          services:service_id (
            name,
            category
          ),
          auth.users:artist_id (
            email
          )
        `)
        .eq('qr_code_id', qrCodeId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (bookingsError) {
        console.warn(`[${requestId}] Error fetching booking details:`, bookingsError);
      } else {
        bookingDetails = bookings || [];
      }
    }

    // Get revenue breakdown by artist and service if requested
    let revenueBreakdown = null;
    if (include_revenue_breakdown === 'true') {
      const { data: revenueData, error: revenueError } = await supabase
        .from('event_bookings')
        .select(`
          artist_id,
          service_id,
          revenue_amount,
          artist_earnings,
          platform_earnings,
          payment_status,
          auth.users:artist_id (
            email
          ),
          services:service_id (
            name,
            category
          )
        `)
        .eq('qr_code_id', qrCodeId)
        .eq('payment_status', 'completed')
        .gte('created_at', startDate.toISOString());

      if (revenueError) {
        console.warn(`[${requestId}] Error fetching revenue breakdown:`, revenueError);
      } else {
        // Process revenue breakdown
        revenueBreakdown = processRevenueBreakdown(revenueData || []);
      }
    }

    // Get daily revenue trends
    const { data: dailyRevenue, error: dailyError } = await supabase
      .from('event_bookings')
      .select('revenue_amount, created_at, payment_status')
      .eq('qr_code_id', qrCodeId)
      .eq('payment_status', 'completed')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    let dailyTrends = [];
    if (!dailyError && dailyRevenue) {
      dailyTrends = processDailyTrends(dailyRevenue, parseInt(date_range));
    }

    // Calculate performance metrics
    const performanceMetrics = calculatePerformanceMetrics(analytics, dailyRevenue || []);

    const response = {
      qr_code: analytics.qrCode,
      event: analytics.event,
      usage_metrics: analytics.usage,
      revenue_metrics: analytics.revenue,
      performance_metrics: performanceMetrics,
      analytics_data: analytics.analytics,
      daily_trends: dailyTrends,
      date_range: {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        days: parseInt(date_range)
      }
    };

    // Add optional data if requested
    if (bookingDetails) {
      response.booking_details = bookingDetails;
    }

    if (revenueBreakdown) {
      response.revenue_breakdown = revenueBreakdown;
    }

    console.log(`[${requestId}] Successfully fetched analytics for QR code: ${qrCodeId}`);
    
    return res.status(200).json(response);

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetAnalytics:`, error);
    throw error;
  }
}

/**
 * Process revenue breakdown by artist and service
 */
function processRevenueBreakdown(revenueData) {
  const artistBreakdown = {};
  const serviceBreakdown = {};

  revenueData.forEach(booking => {
    const artistEmail = booking.auth?.users?.email || 'Unknown Artist';
    const serviceName = booking.services?.name || 'Unknown Service';
    const revenue = parseFloat(booking.revenue_amount || 0);
    const artistEarnings = parseFloat(booking.artist_earnings || 0);

    // Artist breakdown
    if (!artistBreakdown[artistEmail]) {
      artistBreakdown[artistEmail] = {
        total_revenue: 0,
        total_earnings: 0,
        booking_count: 0
      };
    }
    artistBreakdown[artistEmail].total_revenue += revenue;
    artistBreakdown[artistEmail].total_earnings += artistEarnings;
    artistBreakdown[artistEmail].booking_count += 1;

    // Service breakdown
    if (!serviceBreakdown[serviceName]) {
      serviceBreakdown[serviceName] = {
        total_revenue: 0,
        booking_count: 0,
        category: booking.services?.category || 'general'
      };
    }
    serviceBreakdown[serviceName].total_revenue += revenue;
    serviceBreakdown[serviceName].booking_count += 1;
  });

  return {
    by_artist: artistBreakdown,
    by_service: serviceBreakdown
  };
}

/**
 * Process daily revenue trends
 */
function processDailyTrends(revenueData, days) {
  const dailyData = {};
  
  // Initialize all days with zero values
  for (let i = 0; i < days; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateKey = date.toISOString().split('T')[0];
    dailyData[dateKey] = {
      date: dateKey,
      revenue: 0,
      bookings: 0
    };
  }

  // Populate with actual data
  revenueData.forEach(booking => {
    const dateKey = booking.created_at.split('T')[0];
    if (dailyData[dateKey]) {
      dailyData[dateKey].revenue += parseFloat(booking.revenue_amount || 0);
      dailyData[dateKey].bookings += 1;
    }
  });

  return Object.values(dailyData).sort((a, b) => a.date.localeCompare(b.date));
}

/**
 * Calculate performance metrics
 */
function calculatePerformanceMetrics(analytics, revenueData) {
  const totalScans = analytics.usage.totalScans || 0;
  const totalBookings = analytics.revenue.bookingCount || 0;
  const totalRevenue = analytics.revenue.totalRevenue || 0;

  // Calculate conversion funnel
  const conversionRate = totalScans > 0 ? (totalBookings / totalScans * 100) : 0;
  
  // Calculate revenue per scan
  const revenuePerScan = totalScans > 0 ? (totalRevenue / totalScans) : 0;

  // Calculate recent performance (last 7 days)
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  const recentBookings = revenueData.filter(booking => 
    new Date(booking.created_at) >= sevenDaysAgo
  );
  
  const recentRevenue = recentBookings.reduce((sum, booking) => 
    sum + parseFloat(booking.revenue_amount || 0), 0
  );

  return {
    conversion_rate: Math.round(conversionRate * 100) / 100,
    revenue_per_scan: Math.round(revenuePerScan * 100) / 100,
    recent_performance: {
      bookings_last_7_days: recentBookings.length,
      revenue_last_7_days: Math.round(recentRevenue * 100) / 100,
      average_daily_revenue: Math.round((recentRevenue / 7) * 100) / 100
    },
    efficiency_score: calculateEfficiencyScore(conversionRate, revenuePerScan, totalBookings)
  };
}

/**
 * Calculate efficiency score (0-100)
 */
function calculateEfficiencyScore(conversionRate, revenuePerScan, totalBookings) {
  // Weighted scoring based on conversion rate, revenue efficiency, and volume
  const conversionScore = Math.min(conversionRate * 2, 40); // Max 40 points
  const revenueScore = Math.min(revenuePerScan, 30); // Max 30 points
  const volumeScore = Math.min(totalBookings * 2, 30); // Max 30 points
  
  return Math.round(conversionScore + revenueScore + volumeScore);
}
