/**
 * User Profiles Management API Client
 *
 * This module provides a client-side interface for the user profiles management API.
 * It handles authentication, error handling, and data formatting for:
 * - Role permissions management
 * - User profile settings
 * - Commission rates
 * - User profiles overview
 */
import apiFetch from './api-fetch';
import { getApiUrl } from './cross-origin';

/**
 * Fetch role permissions matrix from the database
 *
 * @returns {Promise<Object>} - { permissions }
 */
export async function fetchPermissions() {
  try {
    console.log('User Profiles: Fetching role permissions...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/permissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl);

    console.log('User Profiles: Permissions fetched successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error fetching permissions:', error);
    throw error;
  }
}

/**
 * Save role permissions matrix to the database
 *
 * @param {Object} permissions - Permission matrix object
 * @returns {Promise<Object>} - Save result
 */
export async function savePermissions(permissions) {
  try {
    console.log('User Profiles: Saving role permissions...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/permissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({ permissions })
    });

    console.log('User Profiles: Permissions saved successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error saving permissions:', error);
    throw error;
  }
}

/**
 * Fetch user profile settings and dashboard widgets
 *
 * @param {string} userId - User ID (optional, defaults to current user)
 * @returns {Promise<Object>} - { settings, dashboardWidgets }
 */
export async function fetchUserSettings(userId = null) {
  try {
    console.log('User Profiles: Fetching user settings...');

    const queryParams = userId ? `?user_id=${userId}` : '';
    const apiUrl = getApiUrl(`/api/admin/user-profiles/settings${queryParams}`);
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl);

    console.log('User Profiles: User settings fetched successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error fetching user settings:', error);
    throw error;
  }
}

/**
 * Save user profile settings and dashboard widgets
 *
 * @param {string} userId - User ID (optional, defaults to current user)
 * @param {Object} settings - Profile settings object
 * @param {Array} dashboardWidgets - Dashboard widgets array
 * @returns {Promise<Object>} - Save result
 */
export async function saveUserSettings(userId = null, settings = null, dashboardWidgets = null) {
  try {
    console.log('User Profiles: Saving user settings...');

    const queryParams = userId ? `?user_id=${userId}` : '';
    const apiUrl = getApiUrl(`/api/admin/user-profiles/settings${queryParams}`);
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify({ settings, dashboardWidgets })
    });

    console.log('User Profiles: User settings saved successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error saving user settings:', error);
    throw error;
  }
}

/**
 * Reset user profile settings to defaults
 *
 * @param {string} userId - User ID (optional, defaults to current user)
 * @returns {Promise<Object>} - Reset result
 */
export async function resetUserSettings(userId = null) {
  try {
    console.log('User Profiles: Resetting user settings...');

    const queryParams = userId ? `?user_id=${userId}` : '';
    const apiUrl = getApiUrl(`/api/admin/user-profiles/settings${queryParams}`);
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'DELETE'
    });

    console.log('User Profiles: User settings reset successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error resetting user settings:', error);
    throw error;
  }
}

/**
 * Fetch commission rates for all users
 *
 * @returns {Promise<Object>} - { commissionRates, users }
 */
export async function fetchCommissionRates() {
  try {
    console.log('User Profiles: Fetching commission rates...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/commissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl);

    console.log('User Profiles: Commission rates fetched successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error fetching commission rates:', error);
    throw error;
  }
}

/**
 * Save a new commission rate
 *
 * @param {Object} rateData - Commission rate data
 * @returns {Promise<Object>} - Save result
 */
export async function saveCommissionRate(rateData) {
  try {
    console.log('User Profiles: Saving commission rate...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/commissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'POST',
      body: JSON.stringify(rateData)
    });

    console.log('User Profiles: Commission rate saved successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error saving commission rate:', error);
    throw error;
  }
}

/**
 * Update an existing commission rate
 *
 * @param {string} rateId - Commission rate ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Object>} - Update result
 */
export async function updateCommissionRate(rateId, updateData) {
  try {
    console.log('User Profiles: Updating commission rate...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/commissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'PUT',
      body: JSON.stringify({ id: rateId, ...updateData })
    });

    console.log('User Profiles: Commission rate updated successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error updating commission rate:', error);
    throw error;
  }
}

/**
 * Delete a commission rate
 *
 * @param {string} rateId - Commission rate ID
 * @returns {Promise<Object>} - Delete result
 */
export async function deleteCommissionRate(rateId) {
  try {
    console.log('User Profiles: Deleting commission rate...');

    const apiUrl = getApiUrl('/api/admin/user-profiles/commissions');
    console.log('User Profiles: Using API URL:', apiUrl);

    const response = await apiFetch(apiUrl, {
      method: 'DELETE',
      body: JSON.stringify({ id: rateId })
    });

    console.log('User Profiles: Commission rate deleted successfully');
    return response;
  } catch (error) {
    console.error('User Profiles: Error deleting commission rate:', error);
    throw error;
  }
}

/**
 * Fetch user profiles overview statistics
 * This uses the existing user management API for now
 *
 * @returns {Promise<Object>} - { stats, recentProfiles }
 */
export async function fetchUserProfilesOverview() {
  try {
    console.log('User Profiles: Fetching overview statistics...');

    // Import user management functions
    const { getUserStats, getRecentActivity } = await import('./user-management');

    // Fetch stats and recent activity
    const [stats, recentActivity] = await Promise.all([
      getUserStats(),
      getRecentActivity()
    ]);

    // Transform recent activity into profile format
    const recentProfiles = recentActivity.map(activity => ({
      id: activity.user_id || activity.id,
      name: activity.user_name || activity.name || 'Unknown User',
      email: activity.user_email || activity.email || '',
      role: activity.user_role || activity.role || 'user',
      lastActive: activity.created_at || activity.last_sign_in_at || new Date().toISOString(),
      profileComplete: Math.floor(Math.random() * 30) + 70 // Mock completion percentage for now
    }));

    console.log('User Profiles: Overview statistics fetched successfully');
    return {
      stats,
      recentProfiles
    };
  } catch (error) {
    console.error('User Profiles: Error fetching overview statistics:', error);
    throw error;
  }
}

export default {
  fetchPermissions,
  savePermissions,
  fetchUserSettings,
  saveUserSettings,
  resetUserSettings,
  fetchCommissionRates,
  saveCommissionRate,
  updateCommissionRate,
  deleteCommissionRate,
  fetchUserProfilesOverview
};
