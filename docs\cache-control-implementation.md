# Cache-Control Headers Implementation

## Overview

This document describes the implementation of cache-control headers for Supabase API requests in the Ocean Soul Sparkles website. The implementation adds appropriate caching strategies based on data freshness requirements and request types.

## Problem Solved

The following Supabase REST API endpoints were missing proper cache-control headers:

1. `/rest/v1/bookings?select=*` (all bookings)
2. `/rest/v1/bookings?select=*&status=eq.pending` (pending bookings)
3. `/rest/v1/customers?select=*` (all customers)
4. `/rest/v1/payments?select=amount&payment_status=eq.completed` (completed payments)
5. `/rest/v1/products?select=*&stock=gt.0&stock=lte.10` (low stock products)
6. `/rest/v1/bookings?select=id%2Cstart_time%2Cend_time%2Cstatus%2Ccustomers%3Acust...` (booking details with customer info)

## Implementation Details

### Files Modified

1. **`lib/supabase.js`** - Enhanced Supabase client configuration
2. **`lib/cache-control-utils.js`** - New utility module for cache management
3. **API route files** - Added cache headers to response objects:
   - `pages/api/admin/bookings/index.js`
   - `pages/api/admin/customers/index.js`
   - `pages/api/admin/payments/index.js`
   - `pages/api/public/products.js`
   - `pages/api/public/services.js`
4. **Client-side components** - Updated fetch calls:
   - `pages/shop.js`

### Cache Strategies

The implementation uses five distinct cache strategies:

#### 1. Real-Time Data (`CACHE_STRATEGIES.REAL_TIME`)
- **Strategy**: `no-cache, no-store, must-revalidate`
- **Use Cases**: 
  - Pending bookings (`status=pending`)
  - Completed payments (`payment_status=completed`)
  - Real-time analytics
- **Rationale**: Financial and booking data that changes frequently and must always be fresh

#### 2. Semi-Static Data (`CACHE_STRATEGIES.SEMI_STATIC`)
- **Strategy**: `max-age=300, stale-while-revalidate=60` (5 minutes cache, 1 minute stale)
- **Use Cases**:
  - Customer data
  - Product inventory
  - General services
- **Rationale**: Data that changes occasionally but can be cached for performance

#### 3. Admin Data (`CACHE_STRATEGIES.ADMIN`)
- **Strategy**: `max-age=60, no-cache` (1 minute cache for admin data)
- **Use Cases**: Admin panel requests for non-real-time data
- **Rationale**: Admin users need relatively fresh data but can tolerate short caching

#### 4. Static Data (`CACHE_STRATEGIES.STATIC`)
- **Strategy**: `max-age=3600, stale-while-revalidate=300` (1 hour cache, 5 minutes stale)
- **Use Cases**:
  - Services with pricing
  - Configuration data
  - Metadata
- **Rationale**: Data that rarely changes and can be cached for longer periods

#### 5. Public Data (`CACHE_STRATEGIES.PUBLIC`)
- **Strategy**: `max-age=180, stale-while-revalidate=30` (3 minutes cache, 30 seconds stale)
- **Use Cases**: Default strategy for public-facing endpoints
- **Rationale**: Balanced approach for general public data

### Priority Logic

The cache strategy selection follows this priority order:

1. **Non-GET requests** → `NO_CACHE`
2. **Real-time data** → `REAL_TIME` (highest priority, even for admin)
3. **Admin requests** → `ADMIN` (for non-real-time data)
4. **Semi-static data** → `SEMI_STATIC`
5. **Static data** → `STATIC`
6. **Default** → `PUBLIC`

## Testing Results

### Public Endpoints

```bash
# Products endpoint (semi-static data)
curl -D - http://localhost:3000/api/public/products
# Returns: Cache-Control: max-age=300, stale-while-revalidate=60

# Services endpoint (static data)
curl -D - http://localhost:3000/api/public/services
# Returns: Cache-Control: max-age=3600, stale-while-revalidate=300
```

### Admin Endpoints

```bash
# Regular admin customers (admin strategy)
curl -D - http://localhost:3000/api/admin/customers
# Returns: Cache-Control: max-age=60, no-cache

# Pending bookings (real-time strategy - overrides admin)
curl -D - "http://localhost:3000/api/admin/bookings?status=pending"
# Returns: Cache-Control: no-cache, no-store, must-revalidate

# Completed payments (real-time strategy - overrides admin)
curl -D - "http://localhost:3000/api/admin/payments?payment_status=completed"
# Returns: Cache-Control: no-cache, no-store, must-revalidate
```

## Key Features

### 1. Intelligent Cache Detection
- Automatically detects real-time data based on endpoint patterns and query parameters
- Prioritizes data freshness requirements over request type

### 2. Flexible Configuration
- Easy to modify cache strategies through `CACHE_STRATEGIES` constants
- Extensible endpoint detection functions

### 3. Development Logging
- Comprehensive logging in development mode
- Cache strategy visibility for debugging

### 4. Client-Side Support
- Utility functions for client-side fetch requests
- Consistent cache headers across server and client

## Benefits

1. **Performance Improvement**: Reduced API calls through intelligent caching
2. **Data Freshness**: Real-time data always fresh, static data efficiently cached
3. **Scalability**: Reduced server load through appropriate caching
4. **User Experience**: Faster page loads for cached data
5. **Cost Optimization**: Fewer Supabase API calls reduce usage costs

## Usage Examples

### Server-Side API Routes

```javascript
import { setCacheHeaders } from '@/lib/cache-control-utils';

export default async function handler(req, res) {
  // ... fetch data logic ...
  
  // Set cache headers based on endpoint and request type
  setCacheHeaders(res, 'customers', 'GET', true, req.query);
  
  return res.status(200).json({ data });
}
```

### Client-Side Components

```javascript
import { fetchWithCache } from '@/lib/cache-control-utils';

// Automatic cache headers based on endpoint
const response = await fetchWithCache('/api/public/products', {}, false);

// Or manually add cache headers
import { getClientCacheHeaders } from '@/lib/cache-control-utils';
const headers = getClientCacheHeaders('/api/admin/bookings', 'GET', true, { status: 'pending' });
```

## Monitoring

The implementation includes development-mode logging to monitor cache strategies:

```
[abc123] Cache strategy: max-age=300, stale-while-revalidate=60
[abc123] Request headers: ['Cache-Control', 'apikey', 'Authorization']
```

This helps developers understand which cache strategy is being applied and verify correct implementation.
