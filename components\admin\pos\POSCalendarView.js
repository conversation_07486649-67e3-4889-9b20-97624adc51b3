import { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import authTokenManager from '@/lib/auth-token-manager';
import { safeRender } from '@/lib/safe-render-utils';
import styles from '@/styles/admin/POS.module.css';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = momentLocalizer(moment);

/**
 * POSCalendarView component for selecting available time slots in POS workflow
 * 
 * @param {Object} props - Component props
 * @param {Object} props.service - Selected service
 * @param {Object} props.tier - Selected pricing tier
 * @param {Function} props.onTimeSlotSelect - Callback when time slot is selected
 * @param {Function} props.onBack - Callback to go back to services
 * @returns {JSX.Element}
 */
export default function POSCalendarView({ service, tier, onTimeSlotSelect, onBack }) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [availableSlots, setAvailableSlots] = useState([]);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('week');

  // Fetch available time slots for the selected service and date
  const fetchAvailableSlots = async (date = selectedDate) => {
    try {
      setLoading(true);
      setError(null);

      const token = authTokenManager.getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const dateStr = moment(date).format('YYYY-MM-DD');
      const response = await fetch(
        `/api/bookings/availability?date=${dateStr}&service_id=${service.id}&duration=${tier.duration}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Failed to fetch availability (${response.status}): ${errorData}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch availability');
      }

      // Transform availability data into calendar events
      const slots = data.availability?.map(slot => ({
        id: slot.id,
        start: new Date(slot.start_time),
        end: new Date(slot.end_time),
        available: slot.available,
        artist: slot.artist,
        title: slot.available ?
          `Available${slot.artist?.artist_name ? ` - ${slot.artist.artist_name}` : ''}` :
          'Booked',
        resource: slot // Store the full slot data
      })) || [];

      setAvailableSlots(slots);

      // Log summary for debugging
      console.log(`Loaded ${slots.length} time slots for ${dateStr}:`, {
        available: slots.filter(s => s.available).length,
        booked: slots.filter(s => !s.available).length,
        service: service.name,
        duration: tier.duration
      });

    } catch (error) {
      console.error('Error fetching availability:', error);
      setError(error.message);
      setAvailableSlots([]); // Clear slots on error
    } finally {
      setLoading(false);
    }
  };

  // Fetch availability when component mounts or date changes
  useEffect(() => {
    if (service && tier) {
      fetchAvailableSlots();
    }
  }, [service, tier, selectedDate]);

  // Handle date navigation
  const handleNavigate = (newDate) => {
    setSelectedDate(newDate);
    setSelectedSlot(null);
  };

  // Handle slot selection from calendar
  const handleSelectSlot = (slotInfo) => {
    // Check if this is a valid time slot selection
    if (slotInfo.start && slotInfo.end) {
      // Find if there's an available slot at this time
      const matchingSlot = availableSlots.find(slot => {
        const slotStart = new Date(slot.start);
        const selectionStart = new Date(slotInfo.start);
        return Math.abs(slotStart.getTime() - selectionStart.getTime()) < 60000; // Within 1 minute
      });

      if (matchingSlot && matchingSlot.available) {
        const slot = {
          start: matchingSlot.start,
          end: matchingSlot.end,
          duration: tier.duration,
          artist: matchingSlot.artist
        };
        setSelectedSlot(slot);
      } else {
        // Show message if slot is not available
        setError('This time slot is not available. Please select an available slot.');
        setTimeout(() => setError(null), 3000);
      }
    }
  };

  // Handle clicking on an existing event (available slot)
  const handleSelectEvent = (event) => {
    if (event.available) {
      const slot = {
        start: event.start,
        end: event.end,
        duration: tier.duration,
        artist: event.artist
      };
      setSelectedSlot(slot);
    } else {
      setError('This time slot is already booked. Please select an available slot.');
      setTimeout(() => setError(null), 3000);
    }
  };

  // Handle time slot confirmation
  const handleConfirmSlot = () => {
    if (selectedSlot && onTimeSlotSelect) {
      onTimeSlotSelect(selectedSlot, selectedSlot.artist);
    }
  };

  // Custom event styling for available slots
  const eventStyleGetter = (event) => {
    if (event.available) {
      return {
        style: {
          backgroundColor: '#4caf50',
          borderColor: '#45a049',
          color: 'white'
        }
      };
    } else {
      return {
        style: {
          backgroundColor: '#f44336',
          borderColor: '#d32f2f',
          color: 'white',
          opacity: 0.6
        }
      };
    }
  };

  if (!service || !tier) {
    return (
      <div className={styles.calendarView}>
        <div className={styles.errorMessage}>
          <h3>Missing Information</h3>
          <p>Please select a service and pricing tier first.</p>
          <button className={styles.backButton} onClick={onBack}>
            ← Back to Services
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.calendarView}>
      <div className={styles.calendarHeader}>
        <div className={styles.headerInfo}>
          <h2>Select Available Time</h2>
          <div className={styles.selectionSummary}>
            <span className={styles.serviceName}>{service.name}</span>
            <span className={styles.tierInfo}>{tier.name} ({tier.duration} min)</span>
            <span className={styles.priceInfo}>${tier.price}</span>
          </div>
        </div>
        <button className={styles.backButton} onClick={onBack}>
          ← Back to Services
        </button>
      </div>

      {error && (
        <div className={styles.errorBox}>
          <p>Error loading availability: {error}</p>
          <button onClick={() => fetchAvailableSlots()}>Retry</button>
        </div>
      )}

      <div className={styles.calendarContainer}>
        {loading ? (
          <div className={styles.loading}>
            <div className={styles.loadingSpinner}></div>
            <p>Loading available time slots...</p>
          </div>
        ) : (
          <>
            <div className={styles.calendarWrapper}>
              <Calendar
                localizer={localizer}
                events={availableSlots}
                startAccessor="start"
                endAccessor="end"
                style={{ height: '400px' }}
                eventPropGetter={eventStyleGetter}
                onSelectSlot={handleSelectSlot}
                onSelectEvent={handleSelectEvent}
                onNavigate={handleNavigate}
                view={view}
                onView={setView}
                date={selectedDate}
                selectable
                popup
                views={['week', 'day']}
                step={15}
                timeslots={4}
                min={new Date(2024, 0, 1, 8, 0)} // 8 AM
                max={new Date(2024, 0, 1, 20, 0)} // 8 PM
                className={styles.posCalendar}
                messages={{
                  allDay: 'All Day',
                  previous: '← Previous',
                  next: 'Next →',
                  today: 'Today',
                  month: 'Month',
                  week: 'Week',
                  day: 'Day',
                  agenda: 'Agenda',
                  date: 'Date',
                  time: 'Time',
                  event: 'Appointment',
                  noEventsInRange: 'No available time slots in this range.',
                  showMore: total => `+${total} more`
                }}
              />
            </div>

            {selectedSlot && (
              <div className={styles.slotConfirmation}>
                <div className={styles.selectedSlotInfo}>
                  <h4>Selected Time Slot</h4>
                  <p><strong>Date:</strong> {moment(selectedSlot.start).format('dddd, MMMM Do YYYY')}</p>
                  <p><strong>Time:</strong> {moment(selectedSlot.start).format('h:mm A')} - {moment(selectedSlot.end).format('h:mm A')}</p>
                  <p><strong>Duration:</strong> {tier.duration} minutes</p>
                  <p><strong>Service:</strong> {service.name} - {tier.name}</p>
                  <p><strong>Price:</strong> ${tier.price}</p>
                </div>
                <div className={styles.confirmationActions}>
                  <button 
                    className={styles.confirmButton}
                    onClick={handleConfirmSlot}
                  >
                    Confirm Time Slot →
                  </button>
                  <button 
                    className={styles.cancelButton}
                    onClick={() => setSelectedSlot(null)}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
