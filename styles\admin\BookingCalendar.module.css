.calendarContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.loading {
  padding: 40px;
  text-align: center;
  color: #6c757d;
  font-size: 1rem;
}

.error {
  padding: 20px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
}

.closeButton {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: #721c24;
}

.closeButton:hover {
  color: #491217;
}

.calendarActions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #6a0dad;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.refreshButton:hover {
  background-color: #5a0b9d;
}

.refreshButton svg {
  width: 16px;
  height: 16px;
}

/* Calendar wrapper for dynamic height */
.calendarWrapper {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* Override react-big-calendar styles */
.calendar {
  flex: 1;
  min-height: 0;
}

.calendar :global(.rbc-toolbar) {
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.calendar :global(.rbc-toolbar button) {
  color: #495057;
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 6px 12px;
  font-weight: 500;
}

.calendar :global(.rbc-toolbar button:hover) {
  background-color: #f8f9fa;
}

.calendar :global(.rbc-toolbar button.rbc-active) {
  background-color: #6a0dad;
  color: white;
  border-color: #6a0dad;
}

.calendar :global(.rbc-toolbar button.rbc-active:hover) {
  background-color: #5a0b9d;
}

.calendar :global(.rbc-month-view),
.calendar :global(.rbc-time-view),
.calendar :global(.rbc-agenda-view) {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  flex: 1;
  min-height: 0;
  overflow: auto;
}

.calendar :global(.rbc-header) {
  padding: 10px;
  font-weight: 600;
  background-color: #f8f9fa;
}

.calendar :global(.rbc-date-cell) {
  padding: 5px 8px;
  text-align: right;
}

.calendar :global(.rbc-off-range-bg) {
  background-color: #f8f9fa;
}

.calendar :global(.rbc-today) {
  background-color: #e6f7ff;
}

.calendar :global(.rbc-event) {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
}

.calendar :global(.rbc-event-label) {
  font-size: 0.75rem;
  margin-bottom: 2px;
}

.calendar :global(.rbc-show-more) {
  color: #6a0dad;
  font-weight: 500;
}

.calendar :global(.rbc-agenda-empty) {
  padding: 30px;
  text-align: center;
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .calendarContainer {
    padding: 8px;
  }

  .calendarActions {
    margin-bottom: 8px;
  }

  .refreshButton {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .calendar :global(.rbc-toolbar) {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 8px;
    padding-bottom: 8px;
  }

  .calendar :global(.rbc-toolbar-label) {
    margin: 8px 0;
    font-size: 0.9rem;
  }

  .calendar :global(.rbc-btn-group) {
    margin-bottom: 8px;
  }

  .calendar :global(.rbc-toolbar button) {
    padding: 4px 8px;
    font-size: 0.8rem;
  }

  .calendar :global(.rbc-header) {
    padding: 6px 8px;
    font-size: 0.8rem;
  }

  .calendar :global(.rbc-event) {
    padding: 2px 4px;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .calendarContainer {
    padding: 6px;
  }

  .calendar :global(.rbc-toolbar) {
    margin-bottom: 6px;
  }

  .calendar :global(.rbc-toolbar button) {
    padding: 3px 6px;
    font-size: 0.75rem;
  }

  .calendar :global(.rbc-header) {
    padding: 4px 6px;
    font-size: 0.75rem;
  }
}
