# POS Payment & Booking Fixes - Comprehensive Testing Guide

## 🔧 **Critical Issues Fixed**

### **Issue 1: Credit Card Payment Failure**
**Root Cause**: Square payment tokenization and API processing issues
**Fix Applied**: Enhanced error handling, token validation, and detailed logging

### **Issue 2: Cash Payment Booking Failure**
**Root Cause**: Database constraint violation - customers table required non-null email
**Fix Applied**: Database schema update to allow null emails for anonymous customers

---

## 🗄️ **Database Schema Changes**

### **Customers Table Updates**
```sql
-- Removed NOT NULL constraint from email field
ALTER TABLE public.customers ALTER COLUMN email DROP NOT NULL;

-- Added POS-specific columns
ALTER TABLE public.customers 
ADD COLUMN is_anonymous BOOLEAN DEFAULT FALSE,
ADD COLUMN pos_customer BOOLEAN DEFAULT FALSE,
ADD COLUMN customer_type TEXT DEFAULT 'regular';
```

### **Bookings Table Updates**
```sql
-- Added POS tracking columns
ALTER TABLE public.bookings 
ADD COLUMN booking_source TEXT DEFAULT 'admin',
ADD COLUMN pos_session_id TEXT,
ADD COLUMN tier_name TEXT,
ADD COLUMN tier_price DECIMAL(10, 2);
```

### **Payments Table Updates**
```sql
-- Added Square payment tracking
ALTER TABLE public.payments 
ADD COLUMN pos_session_id TEXT,
ADD COLUMN square_payment_id TEXT,
ADD COLUMN receipt_url TEXT;
```

---

## 🧪 **Testing Instructions**

### **Phase 1: Cash Payment Testing**
1. **Access POS Terminal**: Navigate to `https://www.oceansoulsparkles.com.au/admin/pos`
2. **Select Service**: Choose "Professional Photo Shoot - One Hour"
3. **Choose Payment Method**: Select "Cash"
4. **Fill Customer Details**: 
   - **Anonymous Customer**: Leave email blank, provide name only
   - **Named Customer**: Provide name and email
5. **Process Payment**: Click "Complete Cash Payment"
6. **Expected Result**: ✅ Booking should be created successfully

### **Phase 2: Credit Card Payment Testing**
1. **Access POS Terminal**: Same as above
2. **Select Service**: Choose "Professional Photo Shoot - One Hour"
3. **Choose Payment Method**: Select "Credit Card"
4. **Wait for Form**: Square payment form should load within 3 seconds
5. **Enter Test Card Details**:
   - **Card Number**: `4111 1111 1111 1111` (Visa test card)
   - **Expiry**: Any future date (e.g., `12/25`)
   - **CVV**: `123`
   - **ZIP**: `12345`
6. **Process Payment**: Click "Charge $X.XX"
7. **Expected Result**: ✅ Payment should process and booking should be created

---

## 🔍 **Debugging & Monitoring**

### **Console Logs to Monitor**
```javascript
// Successful Cash Payment Flow
✅ POS Create Booking API called: POST
✅ Authentication successful. User: <EMAIL>, Role: admin
✅ Creating POS booking for service: Professional Photo Shoot - One Hour
✅ Created anonymous customer: [customer-id]
✅ Created POS booking: [booking-id] for session: pos_[timestamp]_[random]
✅ Created POS payment: [payment-id] for session: pos_[timestamp]_[random]

// Successful Credit Card Payment Flow
✅ Square token validation: {isValid: true, hasValidPrefix: true, ...}
✅ Making Square API request: {amount: 15000, currency: "AUD", ...}
✅ Square API response status: 200
✅ Square payment successful: [square-payment-id]
```

### **Error Patterns to Watch For**
```javascript
// Database Constraint Errors (Should be FIXED)
❌ Failed to create anonymous customer: null value in column "email"
❌ duplicate key value violates unique constraint "customers_email_key"

// Square Payment Errors (May still occur with invalid test data)
❌ Square token validation: {isValid: false, hasValidPrefix: false, ...}
❌ Square API error response: {status: 400, data: {...}}
❌ Invalid Square payment token format
```

---

## 🛠️ **Troubleshooting Guide**

### **If Cash Payments Still Fail**
1. **Check Database Migration**: Verify email constraint was removed
   ```sql
   -- Run in Supabase SQL Editor
   SELECT column_name, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'customers' AND column_name = 'email';
   -- Should show: is_nullable = YES
   ```

2. **Check New Columns**: Verify POS columns were added
   ```sql
   SELECT column_name 
   FROM information_schema.columns 
   WHERE table_name = 'customers' 
     AND column_name IN ('is_anonymous', 'pos_customer', 'customer_type');
   ```

### **If Credit Card Payments Still Fail**
1. **Verify Square Configuration**: Check environment variables
   ```javascript
   // Run in browser console
   console.log({
     appId: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
     locationId: process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID,
     environment: 'sandbox'
   });
   ```

2. **Check Square Token**: Verify token format in console
   ```javascript
   // Look for logs like:
   Square token validation: {
     isValid: true,
     hasValidPrefix: true,
     tokenType: "cnon:",
     length: 64
   }
   ```

3. **Test with Different Cards**: Try alternative test cards
   - **Mastercard**: `5555 5555 5555 4444`
   - **Amex**: `3782 822463 10005`
   - **Declined Card**: `4000 0000 0000 0002`

---

## 📊 **Success Metrics**

### **Cash Payment Success Indicators**
- ✅ Anonymous customers created with `email: null`
- ✅ Bookings created with `booking_source: 'pos'`
- ✅ Payments created with `payment_method: 'cash'`
- ✅ No database constraint violations

### **Credit Card Payment Success Indicators**
- ✅ Square payment form loads within 3 seconds
- ✅ Token validation passes
- ✅ Square API returns 200 status
- ✅ Payment record includes `square_payment_id`
- ✅ Booking created with payment details

---

## 🚀 **Deployment Status**

**Database Migration**: ✅ Applied to production
**API Updates**: ✅ Deployed to production
**Frontend Fixes**: ✅ Container initialization fixed
**Testing**: 🔄 Ready for validation

**Production URL**: `https://www.oceansoulsparkles.com.au/admin/pos`

---

## 📝 **Next Steps**

1. **Test Both Payment Methods**: Verify cash and credit card payments work
2. **Monitor Error Logs**: Check for any remaining issues
3. **Validate Database Records**: Ensure bookings and payments are created correctly
4. **Performance Testing**: Test multiple transactions in sequence
5. **User Acceptance Testing**: Have end users validate the fixes

---

## 🆘 **Emergency Rollback**

If critical issues persist, the database changes can be rolled back:

```sql
-- EMERGENCY ROLLBACK (USE ONLY IF NECESSARY)
-- Re-add NOT NULL constraint to email (will fail if NULL emails exist)
-- ALTER TABLE public.customers ALTER COLUMN email SET NOT NULL;

-- Remove POS columns
-- ALTER TABLE public.customers DROP COLUMN IF EXISTS is_anonymous;
-- ALTER TABLE public.customers DROP COLUMN IF EXISTS pos_customer;
-- ALTER TABLE public.customers DROP COLUMN IF EXISTS customer_type;
```

**⚠️ Warning**: Rollback will break any existing anonymous customers. Only use if absolutely necessary.
