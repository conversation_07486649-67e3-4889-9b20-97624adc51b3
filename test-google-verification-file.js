/**
 * Test script for Google Search Console verification HTML file upload
 * 
 * This script tests:
 * 1. Creating a test Google verification HTML file
 * 2. Uploading it via the admin API
 * 3. Verifying the file is accessible at the root URL
 * 4. Testing file management (list, delete)
 */

const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3002';
const TEST_FILENAME = 'google7703f3860eb21a44.html';
const TEST_CONTENT = 'google-site-verification: google7703f3860eb21a44.html';

/**
 * Create a test Google verification file
 */
function createTestVerificationFile() {
  const testFilePath = path.join(__dirname, TEST_FILENAME);
  fs.writeFileSync(testFilePath, TEST_CONTENT, 'utf8');
  console.log(`✅ Created test verification file: ${testFilePath}`);
  return testFilePath;
}

/**
 * Test uploading Google verification file
 */
async function testUploadVerificationFile() {
  console.log('\n🔧 Testing: Google verification file upload...\n');
  
  try {
    // Create test file
    const testFilePath = createTestVerificationFile();
    
    // Prepare form data
    const FormData = require('form-data');
    const form = new FormData();
    form.append('verificationFile', fs.createReadStream(testFilePath));
    
    // Upload file
    const fetch = require('node-fetch');
    const response = await fetch(`${BASE_URL}/api/admin/uploads/google-verification`, {
      method: 'POST',
      body: form,
      headers: {
        ...form.getHeaders(),
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ File uploaded successfully:', result);
      
      // Clean up test file
      fs.unlinkSync(testFilePath);
      
      return result;
    } else {
      console.log('❌ Upload failed:', result);
      
      // Clean up test file
      fs.unlinkSync(testFilePath);
      
      return null;
    }
    
  } catch (error) {
    console.log('❌ Error uploading file:', error.message);
    return null;
  }
}

/**
 * Test if verification file is accessible at root URL
 */
async function testFileAccessibility(filename) {
  console.log('\n🌐 Testing: File accessibility at root URL...\n');
  
  try {
    const fetch = require('node-fetch');
    const response = await fetch(`${BASE_URL}/${filename}`);
    
    if (response.ok) {
      const content = await response.text();
      console.log(`✅ File accessible at /${filename}`);
      console.log(`📄 Content: ${content}`);
      return true;
    } else {
      console.log(`❌ File not accessible at /${filename} (Status: ${response.status})`);
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Error accessing file: ${error.message}`);
    return false;
  }
}

/**
 * Test listing Google verification files
 */
async function testListVerificationFiles() {
  console.log('\n📋 Testing: List verification files...\n');
  
  try {
    const fetch = require('node-fetch');
    const response = await fetch(`${BASE_URL}/api/admin/google-verification-files`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Files listed successfully:');
      result.files.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.filename} (${file.size} bytes)`);
        console.log(`     URL: ${file.url}`);
        console.log(`     Created: ${file.created}`);
      });
      return result.files;
    } else {
      console.log('❌ Failed to list files:', result);
      return [];
    }
    
  } catch (error) {
    console.log('❌ Error listing files:', error.message);
    return [];
  }
}

/**
 * Test deleting Google verification file
 */
async function testDeleteVerificationFile(filename) {
  console.log(`\n🗑️  Testing: Delete verification file (${filename})...\n`);
  
  try {
    const fetch = require('node-fetch');
    const response = await fetch(`${BASE_URL}/api/admin/google-verification-files?filename=${encodeURIComponent(filename)}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ File deleted successfully:', result.message);
      return true;
    } else {
      console.log('❌ Failed to delete file:', result);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Error deleting file:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Google Verification File Upload Tests...\n');
  console.log('=' * 60);
  
  // Test 1: Upload verification file
  const uploadResult = await testUploadVerificationFile();
  
  if (uploadResult) {
    // Test 2: Check file accessibility
    const isAccessible = await testFileAccessibility(uploadResult.filename);
    
    // Test 3: List verification files
    const files = await testListVerificationFiles();
    
    // Test 4: Delete verification file
    if (files.length > 0) {
      await testDeleteVerificationFile(uploadResult.filename);
      
      // Test 5: Verify file is no longer accessible
      console.log('\n🔍 Testing: Verify file deletion...\n');
      const stillAccessible = await testFileAccessibility(uploadResult.filename);
      if (!stillAccessible) {
        console.log('✅ File successfully deleted and no longer accessible');
      } else {
        console.log('❌ File still accessible after deletion');
      }
    }
  } else {
    console.log('\n❌ Cannot proceed with tests - file upload failed');
  }
  
  console.log('\n' + '=' * 60);
  console.log('🏁 Google verification file tests completed!');
  console.log('\nNext steps:');
  console.log('1. Test the admin interface file upload in browser');
  console.log('2. Upload your actual Google verification file');
  console.log('3. Verify with Google Search Console');
}

// Run tests
runAllTests().catch(console.error);
