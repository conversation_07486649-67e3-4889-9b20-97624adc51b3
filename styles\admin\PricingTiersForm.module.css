.pricingTiersForm {
  margin: 1rem 0;
}

.header {
  margin-bottom: 1.5rem;
}

.header h3 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.description {
  margin: 0;
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.tiersContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.tierCard {
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  padding: 1rem;
  background: #f8f9fa;
  position: relative;
}

.tierCard:hover {
  border-color: #3498db;
  box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1);
}

.tierHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e1e8ed;
}

.tierControls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.moveButton {
  background: #ecf0f1;
  border: 1px solid #bdc3c7;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  color: #7f8c8d;
  transition: all 0.2s ease;
}

.moveButton:hover:not(:disabled) {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.moveButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tierNumber {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.removeButton {
  background: #e74c3c;
  border: none;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 14px;
  transition: all 0.2s ease;
}

.removeButton:hover:not(:disabled) {
  background: #c0392b;
}

.removeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #bdc3c7;
}

.tierFields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 120px 120px;
  gap: 1rem;
  align-items: end;
}

@media (max-width: 768px) {
  .formRow {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.formGroup label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 0.9rem;
}

.required {
  color: #e74c3c;
}

.input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.textarea {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  font-family: inherit;
  resize: vertical;
  min-height: 60px;
  transition: border-color 0.2s ease;
}

.textarea:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
  color: #2c3e50;
}

.checkboxLabel input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.addTierButton {
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.addTierButton:hover {
  background: #229954;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
}

.helpText {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.helpText p {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
}

.helpText ul {
  margin: 0;
  padding-left: 1.2rem;
  color: #7f8c8d;
  font-size: 0.85rem;
  line-height: 1.5;
}

.helpText li {
  margin-bottom: 0.25rem;
}

/* Animation for adding/removing tiers */
.tierCard {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
