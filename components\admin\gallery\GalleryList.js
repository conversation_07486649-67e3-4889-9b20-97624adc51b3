import { useState, useEffect, useCallback } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import { safeRender } from '@/lib/safe-render-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/ProductList.module.css'; // Reuse existing styles

export default function GalleryList({ refreshKey, onEditItem, onDeleteItem }) {
  const [galleryItems, setGalleryItems] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedItems, setSelectedItems] = useState(new Set());

  // Debounced search term
  const [debouncedSearch, setDebouncedSearch] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Fetch gallery items
  const fetchGalleryItems = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        sort_by: sortBy,
        sort_order: sortOrder,
      });

      if (debouncedSearch) {
        queryParams.append('search', debouncedSearch);
      }

      if (selectedCategory && selectedCategory !== 'all') {
        queryParams.append('category', selectedCategory);
      }

      const data = await authenticatedFetch(`/api/admin/gallery?${queryParams.toString()}`, {}, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });

      setGalleryItems(data.data || []);
    } catch (err) {
      console.error('Error fetching gallery items:', err);
      setError(err.message);
      toast.error('Failed to load gallery items');
    } finally {
      setLoading(false);
    }
  }, [debouncedSearch, selectedCategory, sortBy, sortOrder]);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const data = await authenticatedFetch('/api/admin/gallery/categories', {}, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });

      setCategories(data.data || []);
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  }, []);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchGalleryItems();
  }, [fetchGalleryItems, refreshKey]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories, refreshKey]);

  // Handle bulk operations
  const handleBulkAction = async (action) => {
    if (selectedItems.size === 0) {
      toast.warning('Please select items first');
      return;
    }

    const itemIds = Array.from(selectedItems);

    try {
      let requestData;

      switch (action) {
        case 'delete':
          if (!confirm(`Are you sure you want to delete ${itemIds.length} items?`)) {
            return;
          }
          requestData = { operation: 'delete', ids: itemIds };
          break;
        case 'activate':
          requestData = { operation: 'update', updates: { ids: itemIds, data: { status: 'active' } } };
          break;
        case 'deactivate':
          requestData = { operation: 'update', updates: { ids: itemIds, data: { status: 'inactive' } } };
          break;
        case 'feature':
          requestData = { operation: 'update', updates: { ids: itemIds, data: { featured: true } } };
          break;
        case 'unfeature':
          requestData = { operation: 'update', updates: { ids: itemIds, data: { featured: false } } };
          break;
        default:
          return;
      }

      const result = await authenticatedFetch('/api/admin/gallery/bulk', {
        method: action === 'delete' ? 'DELETE' : 'PUT',
        body: JSON.stringify(requestData),
      }, {
        redirect: false, // Don't redirect on auth failure, just show error
        notify: true     // Show notifications for errors
      });
      toast.success(result.message);
      setSelectedItems(new Set());
      fetchGalleryItems();
    } catch (err) {
      console.error('Error in bulk operation:', err);
      toast.error('Bulk operation failed');
    }
  };

  // Handle item selection
  const handleItemSelect = (itemId) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.size === galleryItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(galleryItems.map(item => item.id)));
    }
  };

  if (loading && galleryItems.length === 0) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading gallery items...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error: {error}</p>
        <button onClick={fetchGalleryItems} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.productList}>
      {/* Filters and Search */}
      <div className={styles.filters}>
        <div className={styles.searchContainer}>
          <input
            type="text"
            placeholder="Search gallery items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterGroup}>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category.slug} value={category.slug}>
                {safeRender(category.name)}
              </option>
            ))}
          </select>

          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-');
              setSortBy(field);
              setSortOrder(order);
            }}
            className={styles.filterSelect}
          >
            <option value="created_at-desc">Newest First</option>
            <option value="created_at-asc">Oldest First</option>
            <option value="title-asc">Title A-Z</option>
            <option value="title-desc">Title Z-A</option>
            <option value="display_order-asc">Display Order</option>
          </select>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.size > 0 && (
        <div className={styles.bulkActions}>
          <span>{selectedItems.size} items selected</span>
          <div className={styles.bulkButtons}>
            <button onClick={() => handleBulkAction('activate')} className={styles.bulkButton}>
              Activate
            </button>
            <button onClick={() => handleBulkAction('deactivate')} className={styles.bulkButton}>
              Deactivate
            </button>
            <button onClick={() => handleBulkAction('feature')} className={styles.bulkButton}>
              Feature
            </button>
            <button onClick={() => handleBulkAction('unfeature')} className={styles.bulkButton}>
              Unfeature
            </button>
            <button onClick={() => handleBulkAction('delete')} className={styles.bulkButtonDanger}>
              Delete
            </button>
          </div>
        </div>
      )}

      {/* Gallery Items Table */}
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.size === galleryItems.length && galleryItems.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>Image</th>
              <th>Title</th>
              <th>Category</th>
              <th>Status</th>
              <th>Featured</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {galleryItems.map((item) => (
              <tr key={item.id} className={selectedItems.has(item.id) ? styles.selected : ''}>
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => handleItemSelect(item.id)}
                  />
                </td>
                <td>
                  <div className={styles.imageCell}>
                    <img
                      src={safeRender(item.main_image_url)}
                      alt={safeRender(item.title)}
                      className={styles.thumbnailImage}
                      onError={(e) => {
                        e.target.src = '/images/placeholder.svg';
                      }}
                    />
                  </div>
                </td>
                <td>
                  <div className={styles.titleCell}>
                    <strong>{safeRender(item.title)}</strong>
                    {item.description && (
                      <p className={styles.description}>
                        {safeRender(item.description).substring(0, 100)}
                        {item.description.length > 100 ? '...' : ''}
                      </p>
                    )}
                  </div>
                </td>
                <td>
                  <span className={styles.categoryBadge}>
                    {safeRender(item.category)}
                  </span>
                </td>
                <td>
                  <span className={`${styles.statusBadge} ${styles[item.status]}`}>
                    {safeRender(item.status)}
                  </span>
                </td>
                <td>
                  <span className={item.featured ? styles.featured : styles.notFeatured}>
                    {item.featured ? '⭐ Featured' : 'Not Featured'}
                  </span>
                </td>
                <td>
                  {new Date(item.created_at).toLocaleDateString()}
                </td>
                <td>
                  <div className={styles.actionButtons}>
                    <button
                      onClick={() => onEditItem(item)}
                      className={styles.editButton}
                      title="Edit"
                    >
                      ✏️
                    </button>
                    <button
                      onClick={() => onDeleteItem(item)}
                      className={styles.deleteButton}
                      title="Delete"
                    >
                      🗑️
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {galleryItems.length === 0 && !loading && (
          <div className={styles.emptyState}>
            <p>No gallery items found.</p>
            <p>Try adjusting your search or filters, or add some gallery items.</p>
          </div>
        )}
      </div>
    </div>
  );
}
