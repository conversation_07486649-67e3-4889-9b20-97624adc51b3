<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Services Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }
        .service-preview {
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            font-size: 0.9em;
        }
        .visibility-badges {
            display: flex;
            gap: 5px;
            margin-top: 5px;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        .badge-visible { background-color: #d4edda; color: #155724; }
        .badge-hidden { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Enhanced Services Dashboard Test</h1>
        <p>Testing the new visibility controls, quick toggles, and filtering features.</p>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testVisibilityFiltering()">Test Visibility Filtering</button>
            <button onclick="testQuickToggleAPI()">Test Quick Toggle API</button>
            <button onclick="testBulkOperations()">Test Bulk Operations</button>
            <button onclick="testAllFeatures()">Test All Features</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="test-results">
                <div class="info status">Click a test button to start testing...</div>
            </div>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔍 Visibility Filtering</h3>
                <div id="visibility-filtering">
                    <div class="info status">Click "Test Visibility Filtering" to test...</div>
                </div>
            </div>

            <div class="feature-card">
                <h3>⚡ Quick Toggle Features</h3>
                <div id="quick-toggles">
                    <div class="info status">Click "Test Quick Toggle API" to test...</div>
                </div>
            </div>

            <div class="feature-card">
                <h3>📦 Bulk Operations</h3>
                <div id="bulk-operations">
                    <div class="info status">Click "Test Bulk Operations" to test...</div>
                </div>
            </div>

            <div class="feature-card">
                <h3>📊 Service Status Overview</h3>
                <div id="service-overview">
                    <div class="info status">Loading service overview...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateResults();
        }

        function updateResults() {
            const container = document.getElementById('test-results');
            container.innerHTML = testResults.map(result => 
                `<div class="${result.type} status">[${result.timestamp}] ${result.message}</div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateResults();
        }

        async function testVisibilityFiltering() {
            addResult('🔍 Testing visibility filtering...', 'info');
            
            try {
                // Test different visibility filters
                const filters = [
                    { name: 'All Services', params: '' },
                    { name: 'Public Book-Online', params: '?visibility=public' },
                    { name: 'POS Terminal', params: '?visibility=pos' },
                    { name: 'Events Booking', params: '?visibility=events' },
                    { name: 'Hidden Services', params: '?visibility=hidden' }
                ];

                const results = {};
                
                for (const filter of filters) {
                    const response = await fetch(`/api/admin/services/index${filter.params}`);
                    if (!response.ok) {
                        throw new Error(`${filter.name} filter failed: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    results[filter.name] = data.services?.length || 0;
                }

                addResult(`✅ Visibility filtering working:`, 'success');
                Object.entries(results).forEach(([name, count]) => {
                    addResult(`  - ${name}: ${count} services`, 'info');
                });

                // Display results
                displayVisibilityResults(results);

            } catch (error) {
                addResult(`❌ Visibility filtering error: ${error.message}`, 'error');
            }
        }

        async function testQuickToggleAPI() {
            addResult('⚡ Testing quick toggle API...', 'info');
            
            try {
                // Get a service to test with
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                if (services.length === 0) {
                    addResult('❌ No services found to test with', 'error');
                    return;
                }

                const testService = services[0];
                addResult(`📋 Testing with service: "${testService.name}"`, 'info');

                // Test status toggle (simulate)
                addResult(`🔄 Current status: ${testService.status}`, 'info');
                addResult(`🌐 Public visibility: ${testService.visible_on_public}`, 'info');
                addResult(`🏪 POS visibility: ${testService.visible_on_pos}`, 'info');
                addResult(`🎉 Events visibility: ${testService.visible_on_events}`, 'info');

                addResult('✅ Quick toggle API structure verified', 'success');
                
                // Display toggle interface
                displayQuickToggleInterface(testService);

            } catch (error) {
                addResult(`❌ Quick toggle test error: ${error.message}`, 'error');
            }
        }

        async function testBulkOperations() {
            addResult('📦 Testing bulk operations structure...', 'info');
            
            try {
                // Get services for bulk testing
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`📊 Found ${services.length} services for bulk operations`, 'info');
                
                // Analyze bulk operation potential
                const activeServices = services.filter(s => s.status === 'active').length;
                const inactiveServices = services.filter(s => s.status === 'inactive').length;
                const publicVisible = services.filter(s => s.visible_on_public === true).length;
                const posVisible = services.filter(s => s.visible_on_pos === true).length;
                
                addResult(`📈 Service breakdown:`, 'info');
                addResult(`  - Active: ${activeServices}, Inactive: ${inactiveServices}`, 'info');
                addResult(`  - Public visible: ${publicVisible}, POS visible: ${posVisible}`, 'info');
                
                addResult('✅ Bulk operations data structure verified', 'success');
                
                // Display bulk operations interface
                displayBulkOperationsInterface(services);

            } catch (error) {
                addResult(`❌ Bulk operations test error: ${error.message}`, 'error');
            }
        }

        async function testAllFeatures() {
            addResult('🚀 Running comprehensive feature test...', 'info');
            
            await testVisibilityFiltering();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testQuickToggleAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testBulkOperations();
            
            addResult('🎉 All feature tests completed!', 'success');
        }

        function displayVisibilityResults(results) {
            const container = document.getElementById('visibility-filtering');
            
            const resultItems = Object.entries(results).map(([name, count]) => `
                <div class="service-preview">
                    <strong>${name}</strong>: ${count} services
                </div>
            `).join('');
            
            container.innerHTML = `
                <h4>Filter Results:</h4>
                ${resultItems}
                <p><em>Visibility filtering is working correctly!</em></p>
            `;
        }

        function displayQuickToggleInterface(service) {
            const container = document.getElementById('quick-toggles');
            
            container.innerHTML = `
                <h4>Service: ${service.name}</h4>
                <div class="service-preview">
                    <div><strong>Status:</strong> ${service.status}</div>
                    <div class="visibility-badges">
                        <span class="badge ${service.visible_on_public ? 'badge-visible' : 'badge-hidden'}">
                            🌐 Public ${service.visible_on_public ? '✓' : '✗'}
                        </span>
                        <span class="badge ${service.visible_on_pos ? 'badge-visible' : 'badge-hidden'}">
                            🏪 POS ${service.visible_on_pos ? '✓' : '✗'}
                        </span>
                        <span class="badge ${service.visible_on_events ? 'badge-visible' : 'badge-hidden'}">
                            🎉 Events ${service.visible_on_events ? '✓' : '✗'}
                        </span>
                    </div>
                </div>
                <p><em>Quick toggle buttons would appear in the admin interface!</em></p>
            `;
        }

        function displayBulkOperationsInterface(services) {
            const container = document.getElementById('bulk-operations');
            
            const sampleServices = services.slice(0, 3);
            const serviceItems = sampleServices.map(service => `
                <div class="service-preview">
                    <input type="checkbox" id="service-${service.id}" style="margin-right: 8px;">
                    <label for="service-${service.id}">${service.name} (${service.status})</label>
                </div>
            `).join('');
            
            container.innerHTML = `
                <h4>Sample Bulk Selection:</h4>
                ${serviceItems}
                <div style="margin-top: 10px;">
                    <button style="font-size: 0.8rem; padding: 4px 8px;">Activate Selected</button>
                    <button style="font-size: 0.8rem; padding: 4px 8px;">Show on Public</button>
                </div>
                <p><em>Bulk operations interface ready!</em></p>
            `;
        }

        async function loadServiceOverview() {
            try {
                const response = await fetch('/api/admin/services/index');
                if (!response.ok) {
                    throw new Error(`Failed to fetch services: ${response.status}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                const stats = {
                    total: services.length,
                    active: services.filter(s => s.status === 'active').length,
                    inactive: services.filter(s => s.status === 'inactive').length,
                    publicVisible: services.filter(s => s.visible_on_public === true).length,
                    posVisible: services.filter(s => s.visible_on_pos === true).length,
                    eventsVisible: services.filter(s => s.visible_on_events === true).length
                };
                
                document.getElementById('service-overview').innerHTML = `
                    <div class="service-preview">
                        <div><strong>Total Services:</strong> ${stats.total}</div>
                        <div><strong>Active:</strong> ${stats.active} | <strong>Inactive:</strong> ${stats.inactive}</div>
                        <div><strong>Public Visible:</strong> ${stats.publicVisible}</div>
                        <div><strong>POS Visible:</strong> ${stats.posVisible}</div>
                        <div><strong>Events Visible:</strong> ${stats.eventsVisible}</div>
                    </div>
                `;
                
            } catch (error) {
                document.getElementById('service-overview').innerHTML = 
                    `<div class="error status">Error loading overview: ${error.message}</div>`;
            }
        }

        // Auto-load service overview
        window.addEventListener('load', () => {
            setTimeout(loadServiceOverview, 1000);
        });
    </script>
</body>
</html>
