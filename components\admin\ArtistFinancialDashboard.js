import { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import authTokenManager from '@/lib/auth-token-manager';
import styles from '@/styles/admin/ArtistFinancialDashboard.module.css';

/**
 * Artist Financial Dashboard Component
 * Displays comprehensive earnings breakdown and festival participation history
 */
export default function ArtistFinancialDashboard({ artistId, artistName }) {
  const [earnings, setEarnings] = useState([]);
  const [festivalParticipation, setFestivalParticipation] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(true);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  useEffect(() => {
    if (artistId) {
      fetchArtistFinancialData();
    }
  }, [artistId, selectedYear]);

  const fetchArtistFinancialData = async () => {
    try {
      setLoading(true);
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();

      // Fetch earnings data
      const earningsResponse = await fetch(`/api/admin/artists/${artistId}/earnings?year=${selectedYear}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      // Fetch festival participation
      const participationResponse = await fetch(`/api/admin/artists/${artistId}/festival-participation?year=${selectedYear}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (earningsResponse.ok && participationResponse.ok) {
        const earningsData = await earningsResponse.json();
        const participationData = await participationResponse.json();

        setEarnings(earningsData.earnings || []);
        setFestivalParticipation(participationData.participation || []);
        setSummary(earningsData.summary || {});
      } else {
        throw new Error('Failed to fetch financial data');
      }
    } catch (error) {
      console.error('Error fetching artist financial data:', error);
      toast.error('Failed to load financial data');
    } finally {
      setLoading(false);
    }
  };

  const availableYears = [];
  const currentYear = new Date().getFullYear();
  for (let year = currentYear; year >= currentYear - 5; year--) {
    availableYears.push(year);
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading financial data...</p>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.headerInfo}>
          <h2>💰 Financial Dashboard</h2>
          <p>Earnings and festival participation for {artistName}</p>
        </div>
        <div className={styles.yearSelector}>
          <label>Year:</label>
          <select
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
            className={styles.yearSelect}
          >
            {availableYears.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Financial Summary */}
      <div className={styles.summarySection}>
        <h3>📊 {selectedYear} Summary</h3>
        <div className={styles.summaryGrid}>
          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>💵</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                ${(summary.total_gross_revenue || 0).toFixed(2)}
              </div>
              <div className={styles.summaryLabel}>Gross Revenue</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>🎫</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                ${(summary.total_ticket_costs || 0).toFixed(2)}
              </div>
              <div className={styles.summaryLabel}>Festival Tickets</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>💰</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                ${(summary.total_net_earnings || 0).toFixed(2)}
              </div>
              <div className={styles.summaryLabel}>Net Earnings</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>🎪</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {summary.events_participated || 0}
              </div>
              <div className={styles.summaryLabel}>Events</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>📅</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {summary.total_bookings || 0}
              </div>
              <div className={styles.summaryLabel}>Bookings</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>📈</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                ${(summary.avg_booking_value || 0).toFixed(2)}
              </div>
              <div className={styles.summaryLabel}>Avg Booking</div>
            </div>
          </div>
        </div>
      </div>

      {/* Earnings by Event */}
      <div className={styles.earningsSection}>
        <h3>💼 Earnings by Event</h3>
        {earnings.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No earnings recorded for {selectedYear}</p>
          </div>
        ) : (
          <div className={styles.earningsTable}>
            <div className={styles.tableHeader}>
              <div className={styles.tableCell}>Event</div>
              <div className={styles.tableCell}>Date</div>
              <div className={styles.tableCell}>Bookings</div>
              <div className={styles.tableCell}>Gross Revenue</div>
              <div className={styles.tableCell}>Commission</div>
              <div className={styles.tableCell}>Ticket Cost</div>
              <div className={styles.tableCell}>Net Earnings</div>
            </div>
            {earnings.map(earning => (
              <div key={earning.id} className={styles.tableRow}>
                <div className={styles.tableCell}>
                  <div className={styles.eventName}>{earning.event_name}</div>
                  <div className={styles.eventLocation}>{earning.event_location}</div>
                </div>
                <div className={styles.tableCell}>
                  {new Date(earning.event_date).toLocaleDateString()}
                </div>
                <div className={styles.tableCell}>
                  {earning.booking_count}
                </div>
                <div className={styles.tableCell}>
                  ${parseFloat(earning.gross_revenue || 0).toFixed(2)}
                </div>
                <div className={styles.tableCell}>
                  ${parseFloat(earning.commission_amount || 0).toFixed(2)}
                </div>
                <div className={styles.tableCell}>
                  ${parseFloat(earning.festival_ticket_cost || 0).toFixed(2)}
                </div>
                <div className={styles.tableCell}>
                  <span className={styles.netEarnings}>
                    ${parseFloat(earning.net_earnings || 0).toFixed(2)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Festival Participation History */}
      <div className={styles.participationSection}>
        <h3>🎪 Festival Participation History</h3>
        {festivalParticipation.length === 0 ? (
          <div className={styles.emptyState}>
            <p>No festival participation recorded for {selectedYear}</p>
          </div>
        ) : (
          <div className={styles.participationGrid}>
            {festivalParticipation.map(participation => (
              <div key={participation.id} className={styles.participationCard}>
                <div className={styles.participationHeader}>
                  <h4>{participation.event_name}</h4>
                  <span className={`${styles.participationStatus} ${
                    participation.attendance_confirmed ? styles.confirmed : styles.pending
                  }`}>
                    {participation.attendance_confirmed ? 'Confirmed' : 'Pending'}
                  </span>
                </div>

                <div className={styles.participationDetails}>
                  <div className={styles.participationDetail}>
                    <span className={styles.detailLabel}>📍 Location:</span>
                    <span className={styles.detailValue}>{participation.event_location}</span>
                  </div>
                  <div className={styles.participationDetail}>
                    <span className={styles.detailLabel}>📅 Date:</span>
                    <span className={styles.detailValue}>
                      {new Date(participation.event_start_date).toLocaleDateString()} - 
                      {new Date(participation.event_end_date).toLocaleDateString()}
                    </span>
                  </div>
                  {participation.artist_pays_ticket && (
                    <div className={styles.participationDetail}>
                      <span className={styles.detailLabel}>🎫 Ticket Cost:</span>
                      <span className={styles.detailValue}>
                        ${parseFloat(participation.ticket_cost || 0).toFixed(2)}
                        {participation.ticket_paid ? ' (Paid)' : ' (Unpaid)'}
                      </span>
                    </div>
                  )}
                  {participation.check_in_time && (
                    <div className={styles.participationDetail}>
                      <span className={styles.detailLabel}>⏰ Check-in:</span>
                      <span className={styles.detailValue}>
                        {new Date(participation.check_in_time).toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>

                {participation.notes && (
                  <div className={styles.participationNotes}>
                    <strong>Notes:</strong> {participation.notes}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Performance Metrics */}
      <div className={styles.metricsSection}>
        <h3>📈 Performance Metrics</h3>
        <div className={styles.metricsGrid}>
          <div className={styles.metricCard}>
            <div className={styles.metricLabel}>Bookings per Event</div>
            <div className={styles.metricValue}>
              {summary.events_participated > 0 
                ? (summary.total_bookings / summary.events_participated).toFixed(1)
                : '0'
              }
            </div>
          </div>
          <div className={styles.metricCard}>
            <div className={styles.metricLabel}>Revenue per Event</div>
            <div className={styles.metricValue}>
              ${summary.events_participated > 0 
                ? (summary.total_gross_revenue / summary.events_participated).toFixed(2)
                : '0.00'
              }
            </div>
          </div>
          <div className={styles.metricCard}>
            <div className={styles.metricLabel}>Commission Rate</div>
            <div className={styles.metricValue}>
              {summary.total_gross_revenue > 0 
                ? ((summary.total_commission_paid / summary.total_gross_revenue) * 100).toFixed(1)
                : '0'
              }%
            </div>
          </div>
          <div className={styles.metricCard}>
            <div className={styles.metricLabel}>Net Margin</div>
            <div className={styles.metricValue}>
              {summary.total_gross_revenue > 0 
                ? ((summary.total_net_earnings / summary.total_gross_revenue) * 100).toFixed(1)
                : '0'
              }%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
