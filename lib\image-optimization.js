/**
 * Image Optimization and Loading Utilities
 * Provides optimized image loading strategies for better performance
 */

/**
 * Preload images with priority and error handling
 * @param {Array} imageUrls - Array of image URLs to preload
 * @param {Object} options - Preloading options
 * @returns {Promise} - Promise that resolves when preloading is complete
 */
export const preloadImages = async (imageUrls, options = {}) => {
  const {
    priority = 'high',
    timeout = 10000,
    onProgress = null,
    onError = null
  } = options;

  if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
    return Promise.resolve([]);
  }

  const preloadPromises = imageUrls.map((url, index) => {
    return new Promise((resolve) => {
      const img = new Image();
      
      // Set loading priority
      if (priority === 'high') {
        img.loading = 'eager';
        img.fetchPriority = 'high';
      } else {
        img.loading = 'lazy';
        img.fetchPriority = 'low';
      }

      const timeoutId = setTimeout(() => {
        if (onError) onError(url, 'timeout');
        resolve({ url, status: 'timeout', index });
      }, timeout);

      img.onload = () => {
        clearTimeout(timeoutId);
        if (onProgress) onProgress(index + 1, imageUrls.length);
        resolve({ 
          url, 
          status: 'loaded', 
          index,
          dimensions: {
            width: img.naturalWidth,
            height: img.naturalHeight
          }
        });
      };

      img.onerror = () => {
        clearTimeout(timeoutId);
        if (onError) onError(url, 'error');
        resolve({ url, status: 'error', index });
      };

      img.src = url;
    });
  });

  try {
    const results = await Promise.allSettled(preloadPromises);
    return results.map(result => result.value);
  } catch (error) {
    console.error('Error in preloadImages:', error);
    return [];
  }
};

/**
 * Create optimized image element with loading states
 * @param {Object} config - Image configuration
 * @returns {Object} - Image element and state handlers
 */
export const createOptimizedImage = (config) => {
  const {
    src,
    alt = '',
    className = '',
    onLoad = null,
    onError = null,
    priority = false,
    placeholder = null
  } = config;

  const imageProps = {
    src,
    alt,
    className,
    loading: priority ? 'eager' : 'lazy',
    decoding: 'async',
    onLoad: (e) => {
      if (onLoad) onLoad(e);
    },
    onError: (e) => {
      if (placeholder) {
        e.target.src = placeholder;
      }
      if (onError) onError(e);
    }
  };

  // Add fetchPriority for modern browsers
  if (priority && 'fetchPriority' in HTMLImageElement.prototype) {
    imageProps.fetchPriority = 'high';
  }

  return imageProps;
};

/**
 * Batch image loader with progress tracking
 * @param {Array} images - Array of image objects with src and id
 * @param {Function} onProgress - Progress callback
 * @param {Function} onComplete - Completion callback
 * @returns {Function} - Cleanup function
 */
export const batchLoadImages = (images, onProgress, onComplete) => {
  let loadedCount = 0;
  let errorCount = 0;
  const total = images.length;
  const results = {};

  if (total === 0) {
    if (onComplete) onComplete(results);
    return () => {};
  }

  const imageElements = images.map(({ src, id }) => {
    const img = new Image();
    
    img.onload = () => {
      loadedCount++;
      results[id] = { status: 'loaded', src, dimensions: { width: img.naturalWidth, height: img.naturalHeight } };
      
      if (onProgress) {
        onProgress(loadedCount + errorCount, total, 'loaded', id);
      }
      
      if (loadedCount + errorCount === total && onComplete) {
        onComplete(results);
      }
    };

    img.onerror = () => {
      errorCount++;
      results[id] = { status: 'error', src };
      
      if (onProgress) {
        onProgress(loadedCount + errorCount, total, 'error', id);
      }
      
      if (loadedCount + errorCount === total && onComplete) {
        onComplete(results);
      }
    };

    img.src = src;
    return img;
  });

  // Return cleanup function
  return () => {
    imageElements.forEach(img => {
      img.onload = null;
      img.onerror = null;
      img.src = '';
    });
  };
};

/**
 * Progressive image loading strategy
 * Loads images in batches to prevent overwhelming the browser
 * @param {Array} imageUrls - Array of image URLs
 * @param {Object} options - Loading options
 * @returns {Promise} - Promise that resolves when all batches are loaded
 */
export const progressiveImageLoad = async (imageUrls, options = {}) => {
  const {
    batchSize = 6,
    batchDelay = 100,
    onBatchComplete = null,
    onProgress = null
  } = options;

  if (!Array.isArray(imageUrls) || imageUrls.length === 0) {
    return [];
  }

  const batches = [];
  for (let i = 0; i < imageUrls.length; i += batchSize) {
    batches.push(imageUrls.slice(i, i + batchSize));
  }

  const allResults = [];
  let totalLoaded = 0;

  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    
    try {
      const batchResults = await preloadImages(batch, {
        priority: batchIndex === 0 ? 'high' : 'low',
        onProgress: (loaded, total) => {
          if (onProgress) {
            onProgress(totalLoaded + loaded, imageUrls.length);
          }
        }
      });

      allResults.push(...batchResults);
      totalLoaded += batch.length;

      if (onBatchComplete) {
        onBatchComplete(batchIndex + 1, batches.length, batchResults);
      }

      // Add delay between batches (except for the last batch)
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, batchDelay));
      }
    } catch (error) {
      console.error(`Error loading batch ${batchIndex}:`, error);
    }
  }

  return allResults;
};

/**
 * Image cache management
 */
export const ImageCache = {
  cache: new Map(),
  
  /**
   * Add image to cache
   * @param {string} url - Image URL
   * @param {Object} data - Image data
   */
  set(url, data) {
    this.cache.set(url, {
      ...data,
      timestamp: Date.now()
    });
  },

  /**
   * Get image from cache
   * @param {string} url - Image URL
   * @returns {Object|null} - Cached image data or null
   */
  get(url) {
    return this.cache.get(url) || null;
  },

  /**
   * Check if image is cached
   * @param {string} url - Image URL
   * @returns {boolean} - True if cached
   */
  has(url) {
    return this.cache.has(url);
  },

  /**
   * Clear old cache entries
   * @param {number} maxAge - Maximum age in milliseconds (default: 1 hour)
   */
  cleanup(maxAge = 3600000) {
    const now = Date.now();
    for (const [url, data] of this.cache.entries()) {
      if (now - data.timestamp > maxAge) {
        this.cache.delete(url);
      }
    }
  },

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
  }
};

/**
 * Get optimized image loading strategy based on viewport and connection
 * @returns {Object} - Loading strategy configuration
 */
export const getLoadingStrategy = () => {
  if (typeof window === 'undefined') {
    return { strategy: 'eager', batchSize: 6 };
  }

  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
  const isSlowConnection = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
  const isLowMemory = navigator.deviceMemory && navigator.deviceMemory < 4;

  if (isSlowConnection || isLowMemory) {
    return {
      strategy: 'progressive',
      batchSize: 3,
      priority: 'low',
      timeout: 15000
    };
  }

  return {
    strategy: 'batch',
    batchSize: 6,
    priority: 'high',
    timeout: 10000
  };
};
