/**
 * Comprehensive console error capture system for React Error #130 debugging
 * Captures ALL browser console errors and sends them to the terminal
 */

let originalConsoleError = null;
let originalConsoleWarn = null;
let originalConsoleLog = null;
let isCapturing = false;

/**
 * Initialize console error capture
 */
export function initializeConsoleCapture() {
  if (isCapturing || typeof window === 'undefined') {
    return;
  }

  console.log('🔧 Initializing comprehensive console error capture...');

  // Store original console methods
  originalConsoleError = console.error;
  originalConsoleWarn = console.warn;
  originalConsoleLog = console.log;

  // Override console.error
  console.error = function(...args) {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    // Check for React Error #130 patterns
    const isReactError130 = message.includes('Element type is invalid') || 
                           message.includes('Objects are not valid as a React child') ||
                           message.includes('React Error #130') ||
                           message.includes('Minified React error #130') ||
                           message.includes('found object with keys');

    // Enhanced logging for React errors
    if (isReactError130) {
      console.log('🚨 REACT ERROR #130 DETECTED IN CONSOLE!');
      console.log('📋 Full Error Message:', message);
      
      // Try to extract stack trace
      args.forEach((arg, index) => {
        if (arg && arg.stack) {
          console.log(`📊 Stack Trace ${index + 1}:`, arg.stack);
        }
      });

      // Send to terminal immediately
      sendErrorToTerminal({
        type: 'React Error #130',
        message: message,
        args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
        timestamp: new Date().toISOString(),
        url: window.location.href,
        stack: args.find(arg => arg && arg.stack)?.stack || 'No stack trace available'
      });
    } else {
      // Send all other errors too
      sendErrorToTerminal({
        type: 'Console Error',
        message: message,
        args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
    }

    // Call original console.error
    originalConsoleError.apply(console, args);
  };

  // Override console.warn for warnings
  console.warn = function(...args) {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    // Check for React warnings
    if (message.includes('Warning:') || message.includes('React')) {
      sendErrorToTerminal({
        type: 'React Warning',
        message: message,
        args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
        timestamp: new Date().toISOString(),
        url: window.location.href
      });
    }

    // Call original console.warn
    originalConsoleWarn.apply(console, args);
  };

  // Capture unhandled errors
  window.addEventListener('error', (event) => {
    console.log('🚨 Unhandled JavaScript Error:', event.error);
    sendErrorToTerminal({
      type: 'Unhandled Error',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error ? event.error.toString() : 'No error object',
      stack: event.error ? event.error.stack : 'No stack trace',
      timestamp: new Date().toISOString(),
      url: window.location.href
    });
  });

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    console.log('🚨 Unhandled Promise Rejection:', event.reason);
    sendErrorToTerminal({
      type: 'Unhandled Promise Rejection',
      message: event.reason ? event.reason.toString() : 'Unknown rejection',
      reason: event.reason,
      timestamp: new Date().toISOString(),
      url: window.location.href
    });
  });

  isCapturing = true;
  console.log('✅ Console error capture initialized successfully');
}

/**
 * Send error data to terminal via API
 */
async function sendErrorToTerminal(errorData) {
  try {
    await fetch('/api/admin/diagnostics/client-error', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(errorData)
    });
  } catch (e) {
    // Use original console.error to avoid infinite loop
    if (originalConsoleError) {
      originalConsoleError('Failed to send error to terminal:', e);
    }
  }
}

/**
 * Restore original console methods
 */
export function restoreConsole() {
  if (!isCapturing) {
    return;
  }

  if (originalConsoleError) {
    console.error = originalConsoleError;
  }
  if (originalConsoleWarn) {
    console.warn = originalConsoleWarn;
  }
  if (originalConsoleLog) {
    console.log = originalConsoleLog;
  }

  isCapturing = false;
  console.log('🔧 Console capture restored');
}

/**
 * Test the error capture system
 */
export function testErrorCapture() {
  console.log('🧪 Testing error capture system...');
  
  // Test console.error
  console.error('Test error message');
  
  // Test console.warn
  console.warn('Test warning message');
  
  // Test React Error #130 pattern
  console.error('Error: Objects are not valid as a React child (found: object with keys {test}). If you meant to render a collection of children, use an array instead.');
  
  console.log('✅ Error capture test complete');
}

/**
 * Monitor for specific React Error #130 patterns
 */
export function monitorReactError130() {
  console.log('👁️ Starting React Error #130 monitoring...');
  
  // Set up a periodic check for React errors
  const checkInterval = setInterval(() => {
    // This will run every second to check for any missed errors
    // The actual error capture happens in the overridden console methods
  }, 1000);

  // Stop monitoring after 5 minutes
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('⏰ React Error #130 monitoring period complete');
  }, 300000);

  return checkInterval;
}
