.customersPage {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  overflow: hidden;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.header h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

@media (max-width: 768px) {
  .customersPage {
    padding: 12px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 12px;
    padding-bottom: 8px;
  }

  .header h2 {
    font-size: 1.3rem;
    margin-bottom: 8px;
  }
}
