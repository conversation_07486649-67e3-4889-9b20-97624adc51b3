# Event-Based Booking Restriction Implementation Guide

## Overview

This guide provides step-by-step instructions to implement booking restrictions during event periods, ensuring customers cannot book appointments when artists are committed to events.

## Implementation Steps

### Step 1: Enhance Booking Validation

**File: `lib/booking-validation.js`**

Add event conflict checking function:

```javascript
/**
 * Check if a booking conflicts with any active events
 */
export async function checkEventConflicts(startTime, endTime, supabaseClient) {
  try {
    const { data: conflictingEvents, error } = await supabaseClient
      .from('events')
      .select('id, name, start_date, end_date, location')
      .eq('status', 'active')
      .or(`and(start_date.lte.${startTime},end_date.gte.${startTime}),and(start_date.lte.${endTime},end_date.gte.${endTime}),and(start_date.gte.${startTime},end_date.lte.${endTime})`);

    if (error) {
      console.error('Error checking event conflicts:', error);
      return { hasConflicts: false, events: [] };
    }

    return {
      hasConflicts: conflictingEvents && conflictingEvents.length > 0,
      events: conflictingEvents || [],
      conflictCount: conflictingEvents ? conflictingEvents.length : 0
    };
  } catch (error) {
    console.error('Error in checkEventConflicts:', error);
    return { hasConflicts: false, events: [] };
  }
}
```

### Step 2: Update Booking API Validation

**File: `pages/api/bookings/index.js`**

Add event validation to booking creation:

```javascript
import { checkEventConflicts } from '@/lib/booking-validation';

// In the booking creation logic
const eventConflicts = await checkEventConflicts(
  start_time, 
  end_time, 
  supabase
);

if (eventConflicts.hasConflicts) {
  const eventNames = eventConflicts.events.map(e => e.name).join(', ');
  return res.status(400).json({ 
    error: 'Booking not available during event period',
    message: `Artists are committed to the following event(s): ${eventNames}`,
    conflictingEvents: eventConflicts.events
  });
}
```

### Step 3: Update Mobile Booking Interface

**File: `pages/mobile-booking.js`**

Add event checking to time slot validation:

```javascript
const checkTimeSlotAvailability = async (selectedDate, selectedTime) => {
  const startTime = new Date(`${selectedDate}T${selectedTime}`);
  const endTime = new Date(startTime.getTime() + (selectedService.duration * 60000));

  try {
    // Check for event conflicts
    const eventResponse = await fetch('/api/bookings/check-availability', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
        check_events: true
      })
    });

    const availability = await eventResponse.json();
    
    if (!availability.available && availability.eventConflicts) {
      setBookingError(`Artists are at ${availability.eventConflicts[0].name} during this time. Please choose a different slot.`);
      return false;
    }

    return availability.available;
  } catch (error) {
    console.error('Error checking availability:', error);
    return false;
  }
};
```

### Step 4: Create Availability Check API

**File: `pages/api/bookings/check-availability.js`**

```javascript
import { supabase } from '@/lib/supabase';
import { checkEventConflicts } from '@/lib/booking-validation';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { start_time, end_time, check_events = true } = req.body;

    if (!start_time || !end_time) {
      return res.status(400).json({ error: 'Start time and end time are required' });
    }

    let available = true;
    let conflicts = [];
    let eventConflicts = [];

    // Check for event conflicts if requested
    if (check_events) {
      const eventCheck = await checkEventConflicts(start_time, end_time, supabase);
      if (eventCheck.hasConflicts) {
        available = false;
        eventConflicts = eventCheck.events;
      }
    }

    // Check for booking conflicts (existing logic)
    const { data: bookingConflicts, error } = await supabase
      .from('bookings')
      .select('id, start_time, end_time')
      .neq('status', 'canceled')
      .or(`and(start_time.lte.${start_time},end_time.gt.${start_time}),and(start_time.lt.${end_time},end_time.gte.${end_time}),and(start_time.gte.${start_time},end_time.lte.${end_time})`);

    if (error) {
      throw error;
    }

    if (bookingConflicts && bookingConflicts.length > 0) {
      available = false;
      conflicts = bookingConflicts;
    }

    return res.status(200).json({
      available,
      conflicts,
      eventConflicts,
      message: !available ? 
        (eventConflicts.length > 0 ? 
          `Artists are at ${eventConflicts[0].name}` : 
          'Time slot is already booked'
        ) : 'Time slot is available'
    });

  } catch (error) {
    console.error('Error checking availability:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
```

### Step 5: Enhance Calendar Display

**File: `components/admin/BookingCalendar.js`**

Add visual indicators for event periods:

```javascript
// In the eventStyleGetter function, add logic for blocked periods
const eventStyleGetter = (event) => {
  const { resource } = event;
  const { status, serviceColor, type } = resource;

  // Special styling for events (already implemented)
  if (type === 'event') {
    // ... existing event styling
  }

  // Add visual indicator for time slots during events
  if (resource.blockedByEvent) {
    return {
      style: {
        backgroundColor: '#ffebee',
        borderLeft: '4px solid #f44336',
        color: '#d32f2f',
        borderRadius: '4px',
        opacity: 0.7,
        fontStyle: 'italic'
      }
    };
  }

  // ... rest of existing styling logic
};
```

### Step 6: Update Time Slot Generation

**File: `lib/time-slots.js` (create if doesn't exist)**

```javascript
import { supabase } from '@/lib/supabase';

export async function generateAvailableTimeSlots(date, serviceDuration = 30) {
  const slots = [];
  const startHour = 9; // 9 AM
  const endHour = 17; // 5 PM
  
  // Generate base time slots
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += serviceDuration) {
      const slotTime = new Date(date);
      slotTime.setHours(hour, minute, 0, 0);
      
      const endTime = new Date(slotTime.getTime() + (serviceDuration * 60000));
      
      // Check if this slot conflicts with any events
      const { data: events } = await supabase
        .from('events')
        .select('name, start_date, end_date')
        .eq('status', 'active')
        .or(`and(start_date.lte.${slotTime.toISOString()},end_date.gte.${slotTime.toISOString()})`);
      
      const isBlocked = events && events.length > 0;
      
      slots.push({
        time: slotTime,
        endTime: endTime,
        available: !isBlocked,
        blockedReason: isBlocked ? `Artists at ${events[0].name}` : null,
        eventName: isBlocked ? events[0].name : null
      });
    }
  }
  
  return slots;
}
```

## Testing Checklist

### ✅ Event Creation
- [ ] Create a test event for tomorrow
- [ ] Verify event appears on calendar
- [ ] Confirm event has correct styling

### ✅ Booking Restriction
- [ ] Try to book during event period
- [ ] Verify booking is rejected with proper error message
- [ ] Confirm alternative times are suggested

### ✅ Calendar Integration
- [ ] Events display correctly on booking calendar
- [ ] Events are non-draggable
- [ ] Clicking events opens event detail page

### ✅ Mobile Booking
- [ ] Time slots during events show as unavailable
- [ ] Error messages are user-friendly
- [ ] Alternative booking suggestions work

## Deployment Notes

1. **Database Migration**: No schema changes required for basic implementation
2. **API Changes**: New availability check endpoint
3. **Frontend Updates**: Enhanced validation and error handling
4. **Testing**: Comprehensive testing of booking flow during events

## Future Enhancements

1. **Partial Availability**: Allow some services during events
2. **Event Notifications**: Notify customers of event-related restrictions
3. **Waitlist System**: Queue bookings for post-event availability
4. **Smart Suggestions**: Recommend optimal booking times around events

This implementation provides a solid foundation for event-based booking restrictions while maintaining a smooth user experience.
