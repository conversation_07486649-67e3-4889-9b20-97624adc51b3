/**
 * Real-time Admin Error Monitor
 * Captures and displays browser console errors during admin interface testing
 * Specifically designed to catch React Error #130 and related issues
 */

(function() {
  'use strict';
  
  // Error tracking storage
  let errorLog = [];
  let warningLog = [];
  let currentPage = window.location.pathname;
  let pageLoadTime = Date.now();
  
  // Create error display UI
  function createErrorMonitorUI() {
    // Check if UI already exists
    if (document.getElementById('error-monitor-ui')) {
      return;
    }
    
    const monitorUI = document.createElement('div');
    monitorUI.id = 'error-monitor-ui';
    monitorUI.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 500px;
      background: #1a1a1a;
      color: #fff;
      border: 2px solid #ff4444;
      border-radius: 8px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      z-index: 10000;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;
    
    monitorUI.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
        <h3 style="margin: 0; color: #ff4444;">🚨 Error Monitor</h3>
        <div>
          <button id="clear-errors" style="background: #333; color: #fff; border: 1px solid #666; padding: 4px 8px; margin-right: 5px; cursor: pointer;">Clear</button>
          <button id="toggle-monitor" style="background: #333; color: #fff; border: 1px solid #666; padding: 4px 8px; cursor: pointer;">Hide</button>
        </div>
      </div>
      <div id="error-stats" style="margin-bottom: 10px; padding: 8px; background: #2a2a2a; border-radius: 4px;">
        <div>📍 Page: <span id="current-page">${currentPage}</span></div>
        <div>⏱️ Load Time: <span id="load-time">0ms</span></div>
        <div>🚨 Errors: <span id="error-count">0</span></div>
        <div>⚠️ Warnings: <span id="warning-count">0</span></div>
      </div>
      <div id="error-list" style="max-height: 300px; overflow-y: auto;"></div>
    `;
    
    document.body.appendChild(monitorUI);
    
    // Add event listeners
    document.getElementById('clear-errors').addEventListener('click', clearErrors);
    document.getElementById('toggle-monitor').addEventListener('click', toggleMonitor);
    
    updateStats();
  }
  
  // Update statistics display
  function updateStats() {
    const currentPageEl = document.getElementById('current-page');
    const loadTimeEl = document.getElementById('load-time');
    const errorCountEl = document.getElementById('error-count');
    const warningCountEl = document.getElementById('warning-count');
    
    if (currentPageEl) currentPageEl.textContent = currentPage;
    if (loadTimeEl) loadTimeEl.textContent = `${Date.now() - pageLoadTime}ms`;
    if (errorCountEl) errorCountEl.textContent = errorLog.length;
    if (warningCountEl) warningCountEl.textContent = warningLog.length;
  }
  
  // Add error to display
  function addErrorToDisplay(error) {
    const errorList = document.getElementById('error-list');
    if (!errorList) return;
    
    const errorEl = document.createElement('div');
    errorEl.style.cssText = `
      margin-bottom: 8px;
      padding: 8px;
      background: ${error.type === 'error' ? '#4a1a1a' : '#4a4a1a'};
      border-left: 3px solid ${error.type === 'error' ? '#ff4444' : '#ffaa44'};
      border-radius: 4px;
      font-size: 11px;
      line-height: 1.4;
    `;
    
    const timestamp = new Date(error.timestamp).toLocaleTimeString();
    const icon = error.type === 'error' ? '🚨' : '⚠️';
    
    errorEl.innerHTML = `
      <div style="color: #888; margin-bottom: 4px;">${icon} ${timestamp}</div>
      <div style="color: ${error.type === 'error' ? '#ff6666' : '#ffcc66'}; font-weight: bold;">
        ${error.message}
      </div>
      ${error.stack ? `<div style="color: #999; font-size: 10px; margin-top: 4px;">${error.stack.split('\n')[1] || ''}</div>` : ''}
    `;
    
    errorList.insertBefore(errorEl, errorList.firstChild);
    
    // Limit to 20 displayed errors
    while (errorList.children.length > 20) {
      errorList.removeChild(errorList.lastChild);
    }
    
    updateStats();
  }
  
  // Clear all errors
  function clearErrors() {
    errorLog = [];
    warningLog = [];
    const errorList = document.getElementById('error-list');
    if (errorList) {
      errorList.innerHTML = '';
    }
    updateStats();
  }
  
  // Toggle monitor visibility
  function toggleMonitor() {
    const monitor = document.getElementById('error-monitor-ui');
    const button = document.getElementById('toggle-monitor');
    if (monitor.style.display === 'none') {
      monitor.style.display = 'block';
      button.textContent = 'Hide';
    } else {
      monitor.style.display = 'none';
      button.textContent = 'Show';
    }
  }
  
  // Override console methods to capture errors
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  
  console.error = function(...args) {
    const error = {
      type: 'error',
      message: args.join(' '),
      timestamp: new Date().toISOString(),
      stack: new Error().stack,
      page: currentPage
    };
    
    errorLog.push(error);
    addErrorToDisplay(error);
    
    // Check for React Error #130 patterns
    const message = error.message.toLowerCase();
    if (message.includes('element type is invalid') || 
        message.includes('objects are not valid as a react child') ||
        message.includes('cannot read property') ||
        message.includes('cannot read properties of undefined')) {
      
      // Highlight React errors
      const errorList = document.getElementById('error-list');
      if (errorList && errorList.firstChild) {
        errorList.firstChild.style.border = '2px solid #ff0000';
        errorList.firstChild.style.animation = 'pulse 2s infinite';
      }
    }
    
    // Call original console.error
    originalConsoleError.apply(console, args);
  };
  
  console.warn = function(...args) {
    const warning = {
      type: 'warning',
      message: args.join(' '),
      timestamp: new Date().toISOString(),
      page: currentPage
    };
    
    warningLog.push(warning);
    addErrorToDisplay(warning);
    
    // Call original console.warn
    originalConsoleWarn.apply(console, args);
  };
  
  // Monitor page changes
  function updatePageInfo() {
    currentPage = window.location.pathname;
    pageLoadTime = Date.now();
    updateStats();
  }
  
  // Listen for navigation changes
  window.addEventListener('popstate', updatePageInfo);
  
  // Override pushState and replaceState to catch programmatic navigation
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;
  
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    setTimeout(updatePageInfo, 100);
  };
  
  history.replaceState = function(...args) {
    originalReplaceState.apply(history, args);
    setTimeout(updatePageInfo, 100);
  };
  
  // Add CSS for pulse animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(255, 0, 0, 0); }
      100% { box-shadow: 0 0 0 0 rgba(255, 0, 0, 0); }
    }
  `;
  document.head.appendChild(style);
  
  // Initialize UI when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', createErrorMonitorUI);
  } else {
    createErrorMonitorUI();
  }
  
  // Export functions for manual testing
  window.errorMonitor = {
    getErrors: () => errorLog,
    getWarnings: () => warningLog,
    clearErrors: clearErrors,
    exportReport: () => {
      const report = {
        page: currentPage,
        loadTime: Date.now() - pageLoadTime,
        errors: errorLog,
        warnings: warningLog,
        timestamp: new Date().toISOString()
      };
      
      console.log('📊 Error Monitor Report:', report);
      return report;
    },
    checkReactErrors: () => {
      const reactErrors = errorLog.filter(error => {
        const message = error.message.toLowerCase();
        return message.includes('element type is invalid') || 
               message.includes('objects are not valid as a react child') ||
               message.includes('cannot read property') ||
               message.includes('cannot read properties of undefined');
      });
      
      console.log(`🔍 Found ${reactErrors.length} React-related errors:`, reactErrors);
      return reactErrors;
    }
  };
  
  console.log('🚨 Admin Error Monitor loaded successfully!');
  console.log('💡 Use errorMonitor.exportReport() to get detailed error report');
  console.log('🔍 Use errorMonitor.checkReactErrors() to check for React Error #130');
  
})();
