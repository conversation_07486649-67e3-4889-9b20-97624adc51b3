<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Image Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-item {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        .test-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }
        .test-image.loading {
            opacity: 0.5;
        }
        .test-image.loaded {
            opacity: 1;
        }
        .test-image.error {
            opacity: 0.3;
            filter: grayscale(100%);
        }
        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.loading {
            background: #ffc107;
            color: #000;
        }
        .status.loaded {
            background: #28a745;
            color: white;
        }
        .status.error {
            background: #dc3545;
            color: white;
        }
        .test-info {
            margin-top: 10px;
            font-size: 14px;
        }
        .browser-info {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        .results {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
        }
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🖼️ Gallery Image Loading Test</h1>
        <p>Testing image loading compatibility across browsers and modes</p>
    </div>

    <div class="browser-info">
        <h3>Browser Information</h3>
        <div id="browser-details">Loading browser info...</div>
    </div>

    <div class="test-grid" id="test-grid">
        <!-- Test images will be loaded here -->
    </div>

    <div class="results">
        <h3>Test Controls</h3>
        <button class="btn" onclick="runImageTests()">🔄 Run Image Tests</button>
        <button class="btn" onclick="clearResults()">🗑️ Clear Results</button>
        <button class="btn" onclick="exportResults()">📊 Export Results</button>
        
        <h3>Test Results</h3>
        <div id="test-results">Click "Run Image Tests" to start testing...</div>
    </div>

    <script>
        // Test images from the gallery
        const testImages = [
            { src: '/images/gallery/fav/Butterfly.JPG', title: 'Butterfly Face Art' },
            { src: '/images/gallery/fav/Gold Leopard.jpg', title: 'Gold Leopard' },
            { src: '/images/gallery/fav/Soft Face.JPG', title: 'Soft Face Design' },
            { src: '/images/gallery/fav/UV Alex.JPG', title: 'UV Alex' },
            { src: '/images/gallery/fav/Blue Sparkles.JPG', title: 'Blue Sparkles' },
            { src: '/images/gallery/fav/Kids Butterfly.jpg', title: 'Kids Butterfly' }
        ];

        let testResults = [];

        // Initialize page
        function init() {
            displayBrowserInfo();
            createTestGrid();
        }

        // Display browser information
        function displayBrowserInfo() {
            const ua = navigator.userAgent;
            const isPrivate = detectPrivateMode();
            
            const browserInfo = `
                <strong>User Agent:</strong> ${ua}<br>
                <strong>Platform:</strong> ${navigator.platform}<br>
                <strong>Private Mode:</strong> ${isPrivate ? 'Yes' : 'No'}<br>
                <strong>Cookies Enabled:</strong> ${navigator.cookieEnabled}<br>
                <strong>Online:</strong> ${navigator.onLine}<br>
                <strong>Language:</strong> ${navigator.language}
            `;
            
            document.getElementById('browser-details').innerHTML = browserInfo;
        }

        // Detect private mode
        function detectPrivateMode() {
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                return false;
            } catch (e) {
                return true;
            }
        }

        // Create test grid
        function createTestGrid() {
            const grid = document.getElementById('test-grid');
            grid.innerHTML = '';

            testImages.forEach((imageData, index) => {
                const testItem = document.createElement('div');
                testItem.className = 'test-item';
                testItem.innerHTML = `
                    <div class="status loading" id="status-${index}">
                        <span class="spinner"></span>Loading
                    </div>
                    <img class="test-image loading" id="image-${index}" alt="${imageData.title}">
                    <div class="test-info">
                        <strong>${imageData.title}</strong><br>
                        <small>${imageData.src}</small><br>
                        <span id="info-${index}">Preparing to load...</span>
                    </div>
                `;
                grid.appendChild(testItem);
            });
        }

        // Run image loading tests
        function runImageTests() {
            testResults = [];
            document.getElementById('test-results').innerHTML = 'Running tests...';

            testImages.forEach((imageData, index) => {
                testImageLoad(imageData, index);
            });
        }

        // Test individual image loading
        function testImageLoad(imageData, index) {
            const img = document.getElementById(`image-${index}`);
            const status = document.getElementById(`status-${index}`);
            const info = document.getElementById(`info-${index}`);
            const startTime = Date.now();

            // Reset states
            img.className = 'test-image loading';
            status.className = 'status loading';
            status.innerHTML = '<span class="spinner"></span>Loading';
            info.textContent = 'Loading image...';

            // Set up timeout
            const timeout = setTimeout(() => {
                img.className = 'test-image error';
                status.className = 'status error';
                status.textContent = 'Timeout';
                info.textContent = 'Failed to load within 10 seconds';
                
                testResults.push({
                    image: imageData.src,
                    status: 'timeout',
                    loadTime: Date.now() - startTime
                });
                updateResults();
            }, 10000);

            // Handle successful load
            img.onload = function() {
                clearTimeout(timeout);
                const loadTime = Date.now() - startTime;
                
                img.className = 'test-image loaded';
                status.className = 'status loaded';
                status.textContent = 'Loaded';
                info.innerHTML = `
                    Loaded in ${loadTime}ms<br>
                    Size: ${img.naturalWidth}x${img.naturalHeight}px
                `;
                
                testResults.push({
                    image: imageData.src,
                    status: 'success',
                    loadTime: loadTime,
                    dimensions: {
                        width: img.naturalWidth,
                        height: img.naturalHeight
                    }
                });
                updateResults();
            };

            // Handle load error
            img.onerror = function() {
                clearTimeout(timeout);
                const loadTime = Date.now() - startTime;
                
                img.className = 'test-image error';
                status.className = 'status error';
                status.textContent = 'Error';
                info.textContent = 'Failed to load image';
                
                testResults.push({
                    image: imageData.src,
                    status: 'error',
                    loadTime: loadTime
                });
                updateResults();
            };

            // Start loading with cache-busting
            const cacheBuster = `?t=${Date.now()}&r=${Math.random()}`;
            img.src = imageData.src + cacheBuster;
        }

        // Update test results display
        function updateResults() {
            const total = testResults.length;
            const passed = testResults.filter(r => r.status === 'success').length;
            const failed = total - passed;
            const passRate = total > 0 ? (passed / total * 100).toFixed(1) : 0;

            const resultsHtml = `
                <strong>Progress:</strong> ${total}/${testImages.length} tests completed<br>
                <strong>Passed:</strong> ${passed}<br>
                <strong>Failed:</strong> ${failed}<br>
                <strong>Pass Rate:</strong> ${passRate}%<br><br>
                ${testResults.map(r => `
                    <div style="margin: 5px 0; padding: 5px; background: ${r.status === 'success' ? '#d4edda' : '#f8d7da'}; border-radius: 3px;">
                        ${r.image} - ${r.status} (${r.loadTime}ms)
                    </div>
                `).join('')}
            `;
            
            document.getElementById('test-results').innerHTML = resultsHtml;
        }

        // Clear results
        function clearResults() {
            testResults = [];
            createTestGrid();
            document.getElementById('test-results').innerHTML = 'Click "Run Image Tests" to start testing...';
        }

        // Export results
        function exportResults() {
            const data = {
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                platform: navigator.platform,
                privateMode: detectPrivateMode(),
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `gallery-test-results-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
