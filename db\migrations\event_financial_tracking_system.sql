-- =============================================
-- EVENT FINANCIAL TRACKING SYSTEM
-- Ocean Soul Sparkles - Advanced Financial Management
-- =============================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- EXPENSE CATEGORIES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.expense_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  is_predefined BOOLEAN DEFAULT TRUE,
  icon TEXT, -- For UI display
  color TEXT DEFAULT '#6b7280', -- For UI display
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert predefined expense categories
INSERT INTO public.expense_categories (name, description, icon, color) VALUES
('Festival Booth Rental', 'Cost of renting booth/spot at festivals', '🏪', '#f59e0b'),
('Artist Entry Tickets', 'Festival entry tickets paid by artists', '🎫', '#3b82f6'),
('Equipment Rental', 'Rental costs for equipment and tools', '🛠️', '#8b5cf6'),
('Transportation', 'Travel and transportation expenses', '🚗', '#10b981'),
('Marketing Materials', 'Promotional materials and signage', '📢', '#ef4444'),
('Insurance', 'Event insurance and liability coverage', '🛡️', '#6366f1'),
('Permits & Licenses', 'Required permits and licensing fees', '📋', '#f97316'),
('Miscellaneous', 'Other event-related expenses', '📦', '#6b7280')
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- EVENT EXPENSES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.event_expenses (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  category_id UUID REFERENCES public.expense_categories(id),
  expense_name TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
  description TEXT,
  receipt_url TEXT, -- For storing receipt images/documents
  vendor_name TEXT,
  expense_date DATE NOT NULL,
  payment_method TEXT CHECK (payment_method IN ('cash', 'card', 'bank_transfer', 'paypal', 'other')),
  is_reimbursable BOOLEAN DEFAULT FALSE,
  reimbursed BOOLEAN DEFAULT FALSE,
  reimbursed_date DATE,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ARTIST FESTIVAL PARTICIPATION TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.artist_festival_participation (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  artist_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  ticket_cost DECIMAL(10,2) DEFAULT 0.00 CHECK (ticket_cost >= 0),
  artist_pays_ticket BOOLEAN DEFAULT FALSE,
  ticket_paid BOOLEAN DEFAULT FALSE,
  ticket_payment_date DATE,
  attendance_confirmed BOOLEAN DEFAULT FALSE,
  check_in_time TIMESTAMPTZ,
  check_out_time TIMESTAMPTZ,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(event_id, artist_id)
);

-- =============================================
-- ARTIST EARNINGS TRACKING TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.artist_earnings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  event_id UUID REFERENCES public.events(id) ON DELETE CASCADE,
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  gross_revenue DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  commission_rate DECIMAL(5,2) NOT NULL DEFAULT 15.00,
  commission_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  festival_ticket_cost DECIMAL(10,2) DEFAULT 0.00,
  net_earnings DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'processing', 'failed')),
  payment_date DATE,
  payment_method TEXT,
  payment_reference TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- NOTIFICATION PREFERENCES TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.notification_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  email_notifications BOOLEAN DEFAULT TRUE,
  sms_notifications BOOLEAN DEFAULT FALSE,
  push_notifications BOOLEAN DEFAULT TRUE,
  booking_reminders BOOLEAN DEFAULT TRUE,
  reminder_minutes INTEGER DEFAULT 10 CHECK (reminder_minutes > 0),
  payment_notifications BOOLEAN DEFAULT TRUE,
  event_notifications BOOLEAN DEFAULT TRUE,
  marketing_notifications BOOLEAN DEFAULT FALSE,
  phone_number TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- SCHEDULED NOTIFICATIONS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.scheduled_notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL CHECK (notification_type IN ('booking_reminder', 'payment_due', 'event_update')),
  scheduled_time TIMESTAMPTZ NOT NULL,
  sent BOOLEAN DEFAULT FALSE,
  sent_at TIMESTAMPTZ,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  notification_data JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- ENHANCED EVENTS TABLE MODIFICATIONS
-- =============================================

-- Add financial tracking fields to events table
ALTER TABLE public.events 
ADD COLUMN IF NOT EXISTS total_expenses DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS total_revenue DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS net_profit DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS artist_ticket_cost DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS artists_pay_tickets BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS expense_budget DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS revenue_target DECIMAL(10,2);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IF NOT EXISTS idx_event_expenses_event_id ON public.event_expenses(event_id);
CREATE INDEX IF NOT EXISTS idx_event_expenses_category_id ON public.event_expenses(category_id);
CREATE INDEX IF NOT EXISTS idx_event_expenses_date ON public.event_expenses(expense_date);

CREATE INDEX IF NOT EXISTS idx_artist_festival_participation_event_id ON public.artist_festival_participation(event_id);
CREATE INDEX IF NOT EXISTS idx_artist_festival_participation_artist_id ON public.artist_festival_participation(artist_id);

CREATE INDEX IF NOT EXISTS idx_artist_earnings_artist_id ON public.artist_earnings(artist_id);
CREATE INDEX IF NOT EXISTS idx_artist_earnings_event_id ON public.artist_earnings(event_id);
CREATE INDEX IF NOT EXISTS idx_artist_earnings_payment_status ON public.artist_earnings(payment_status);

CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_scheduled_time ON public.scheduled_notifications(scheduled_time);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_sent ON public.scheduled_notifications(sent);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_booking_id ON public.scheduled_notifications(booking_id);

-- =============================================
-- TRIGGERS FOR AUTOMATIC CALCULATIONS
-- =============================================

-- Function to update event financial totals
CREATE OR REPLACE FUNCTION update_event_financial_totals()
RETURNS TRIGGER AS $$
BEGIN
  -- Update total expenses for the event
  UPDATE public.events 
  SET total_expenses = (
    SELECT COALESCE(SUM(amount), 0) 
    FROM public.event_expenses 
    WHERE event_id = COALESCE(NEW.event_id, OLD.event_id)
  ),
  updated_at = NOW()
  WHERE id = COALESCE(NEW.event_id, OLD.event_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger for event expenses
DROP TRIGGER IF EXISTS trigger_update_event_expenses ON public.event_expenses;
CREATE TRIGGER trigger_update_event_expenses
  AFTER INSERT OR UPDATE OR DELETE ON public.event_expenses
  FOR EACH ROW
  EXECUTE FUNCTION update_event_financial_totals();

-- Function to calculate artist net earnings
CREATE OR REPLACE FUNCTION calculate_artist_net_earnings()
RETURNS TRIGGER AS $$
BEGIN
  NEW.commission_amount = NEW.gross_revenue * (NEW.commission_rate / 100);
  NEW.net_earnings = NEW.gross_revenue - NEW.commission_amount - COALESCE(NEW.festival_ticket_cost, 0);
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for artist earnings calculations
DROP TRIGGER IF EXISTS trigger_calculate_artist_earnings ON public.artist_earnings;
CREATE TRIGGER trigger_calculate_artist_earnings
  BEFORE INSERT OR UPDATE ON public.artist_earnings
  FOR EACH ROW
  EXECUTE FUNCTION calculate_artist_net_earnings();

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE public.expense_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.event_expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_festival_participation ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.artist_earnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_notifications ENABLE ROW LEVEL SECURITY;

-- Policies for expense_categories (readable by all authenticated users)
CREATE POLICY "Anyone can view expense categories" ON public.expense_categories
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage expense categories" ON public.expense_categories
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'dev')
    )
  );

-- Policies for event_expenses (admin and event creators)
CREATE POLICY "Admins can manage event expenses" ON public.event_expenses
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'dev')
    )
  );

-- Policies for artist_festival_participation
CREATE POLICY "Artists can view their own participation" ON public.artist_festival_participation
  FOR SELECT USING (artist_id = auth.uid());

CREATE POLICY "Admins can manage artist participation" ON public.artist_festival_participation
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'dev')
    )
  );

-- Policies for artist_earnings
CREATE POLICY "Artists can view their own earnings" ON public.artist_earnings
  FOR SELECT USING (artist_id = auth.uid());

CREATE POLICY "Admins can manage artist earnings" ON public.artist_earnings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'dev')
    )
  );

-- Policies for notification_preferences
CREATE POLICY "Users can manage their own notification preferences" ON public.notification_preferences
  FOR ALL USING (user_id = auth.uid());

-- Policies for scheduled_notifications
CREATE POLICY "Users can view their own scheduled notifications" ON public.scheduled_notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all scheduled notifications" ON public.scheduled_notifications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'dev')
    )
  );

-- =============================================
-- VIEWS FOR ANALYTICS
-- =============================================

-- Event financial summary view
CREATE OR REPLACE VIEW public.event_financial_summary AS
SELECT 
  e.id,
  e.name,
  e.start_date,
  e.end_date,
  e.status,
  COALESCE(e.total_expenses, 0) as total_expenses,
  COALESCE(e.total_revenue, 0) as total_revenue,
  COALESCE(e.net_profit, 0) as net_profit,
  COALESCE(expense_breakdown.expense_count, 0) as expense_count,
  COALESCE(artist_participation.artist_count, 0) as participating_artists,
  COALESCE(artist_participation.total_ticket_costs, 0) as total_artist_ticket_costs
FROM public.events e
LEFT JOIN (
  SELECT 
    event_id,
    COUNT(*) as expense_count
  FROM public.event_expenses
  GROUP BY event_id
) expense_breakdown ON e.id = expense_breakdown.event_id
LEFT JOIN (
  SELECT 
    event_id,
    COUNT(*) as artist_count,
    SUM(CASE WHEN artist_pays_ticket THEN ticket_cost ELSE 0 END) as total_ticket_costs
  FROM public.artist_festival_participation
  GROUP BY event_id
) artist_participation ON e.id = artist_participation.event_id;

-- Artist earnings summary view
CREATE OR REPLACE VIEW public.artist_earnings_summary AS
SELECT 
  ae.artist_id,
  up.name as artist_name,
  COUNT(DISTINCT ae.event_id) as events_participated,
  COUNT(ae.booking_id) as total_bookings,
  SUM(ae.gross_revenue) as total_gross_revenue,
  SUM(ae.commission_amount) as total_commission_paid,
  SUM(ae.festival_ticket_cost) as total_ticket_costs,
  SUM(ae.net_earnings) as total_net_earnings,
  AVG(ae.gross_revenue) as avg_booking_value,
  EXTRACT(YEAR FROM ae.created_at) as year
FROM public.artist_earnings ae
LEFT JOIN public.user_profiles up ON ae.artist_id = up.id
GROUP BY ae.artist_id, up.name, EXTRACT(YEAR FROM ae.created_at);

-- =============================================
-- COMMENTS
-- =============================================

COMMENT ON TABLE public.expense_categories IS 'Categories for organizing event expenses';
COMMENT ON TABLE public.event_expenses IS 'Detailed expense tracking for events';
COMMENT ON TABLE public.artist_festival_participation IS 'Artist participation and ticket cost tracking';
COMMENT ON TABLE public.artist_earnings IS 'Comprehensive artist earnings tracking';
COMMENT ON TABLE public.notification_preferences IS 'User notification preferences and settings';
COMMENT ON TABLE public.scheduled_notifications IS 'Scheduled notifications for bookings and events';
COMMENT ON VIEW public.event_financial_summary IS 'Financial overview for each event';
COMMENT ON VIEW public.artist_earnings_summary IS 'Artist earnings summary by year';
