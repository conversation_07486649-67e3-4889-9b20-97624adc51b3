import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

/**
 * User Data Cleanup API
 * Fixes orphaned records and data inconsistencies
 */
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate request
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ 
        error: 'Unauthorized. Only administrators and developers can perform cleanup operations.' 
      })
    }

    // Get admin client
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    const { action, userIds } = req.body
    const results = {
      timestamp: new Date().toISOString(),
      action,
      processed: 0,
      created: 0,
      deleted: 0,
      errors: [],
      details: []
    }

    if (action === 'create_missing_profiles') {
      // Create missing user_profiles for users in auth.users
      console.log('Creating missing user profiles...')
      
      // Get all auth users
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
        page: 1,
        perPage: 1000
      })
      
      if (authError) {
        throw new Error(`Failed to fetch auth users: ${authError.message}`)
      }

      // Get existing profiles
      const { data: existingProfiles, error: profilesError } = await adminClient
        .from('user_profiles')
        .select('id')
      
      if (profilesError) {
        throw new Error(`Failed to fetch existing profiles: ${profilesError.message}`)
      }

      const existingProfileIds = new Set(existingProfiles.map(p => p.id))
      const missingProfiles = authData.users.filter(u => !existingProfileIds.has(u.id))

      if (userIds && userIds.length > 0) {
        // Filter to specific user IDs if provided
        missingProfiles = missingProfiles.filter(u => userIds.includes(u.id))
      }

      results.processed = missingProfiles.length

      for (const authUser of missingProfiles) {
        try {
          const { error: insertError } = await adminClient
            .from('user_profiles')
            .insert([
              {
                id: authUser.id,
                name: authUser.user_metadata?.name || authUser.email,
                created_at: authUser.created_at,
                updated_at: new Date().toISOString()
              }
            ])

          if (insertError) {
            results.errors.push(`Failed to create profile for ${authUser.email}: ${insertError.message}`)
          } else {
            results.created++
            results.details.push(`Created profile for ${authUser.email}`)
          }
        } catch (error) {
          results.errors.push(`Error creating profile for ${authUser.email}: ${error.message}`)
        }
      }

    } else if (action === 'create_missing_roles') {
      // Create missing user_roles for users in auth.users
      console.log('Creating missing user roles...')
      
      // Get all auth users
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
        page: 1,
        perPage: 1000
      })
      
      if (authError) {
        throw new Error(`Failed to fetch auth users: ${authError.message}`)
      }

      // Get existing roles
      const { data: existingRoles, error: rolesError } = await adminClient
        .from('user_roles')
        .select('id')
      
      if (rolesError) {
        throw new Error(`Failed to fetch existing roles: ${rolesError.message}`)
      }

      const existingRoleIds = new Set(existingRoles.map(r => r.id))
      let missingRoles = authData.users.filter(u => !existingRoleIds.has(u.id))

      if (userIds && userIds.length > 0) {
        // Filter to specific user IDs if provided
        missingRoles = missingRoles.filter(u => userIds.includes(u.id))
      }

      results.processed = missingRoles.length

      for (const authUser of missingRoles) {
        try {
          const { error: insertError } = await adminClient
            .from('user_roles')
            .insert([
              {
                id: authUser.id,
                role: 'user', // Default role
                created_at: authUser.created_at,
                updated_at: new Date().toISOString()
              }
            ])

          if (insertError) {
            results.errors.push(`Failed to create role for ${authUser.email}: ${insertError.message}`)
          } else {
            results.created++
            results.details.push(`Created role for ${authUser.email}`)
          }
        } catch (error) {
          results.errors.push(`Error creating role for ${authUser.email}: ${error.message}`)
        }
      }

    } else if (action === 'delete_orphaned_profiles') {
      // Delete user_profiles that don't have corresponding auth.users
      console.log('Deleting orphaned user profiles...')
      
      // Get all auth users
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
        page: 1,
        perPage: 1000
      })
      
      if (authError) {
        throw new Error(`Failed to fetch auth users: ${authError.message}`)
      }

      const authUserIds = new Set(authData.users.map(u => u.id))

      // Get all profiles
      const { data: allProfiles, error: profilesError } = await adminClient
        .from('user_profiles')
        .select('id, name')
      
      if (profilesError) {
        throw new Error(`Failed to fetch profiles: ${profilesError.message}`)
      }

      let orphanedProfiles = allProfiles.filter(p => !authUserIds.has(p.id))

      if (userIds && userIds.length > 0) {
        // Filter to specific user IDs if provided
        orphanedProfiles = orphanedProfiles.filter(p => userIds.includes(p.id))
      }

      results.processed = orphanedProfiles.length

      for (const profile of orphanedProfiles) {
        try {
          const { error: deleteError } = await adminClient
            .from('user_profiles')
            .delete()
            .eq('id', profile.id)

          if (deleteError) {
            results.errors.push(`Failed to delete orphaned profile ${profile.id}: ${deleteError.message}`)
          } else {
            results.deleted++
            results.details.push(`Deleted orphaned profile for ${profile.name || profile.id}`)
          }
        } catch (error) {
          results.errors.push(`Error deleting orphaned profile ${profile.id}: ${error.message}`)
        }
      }

    } else if (action === 'delete_orphaned_roles') {
      // Delete user_roles that don't have corresponding auth.users
      console.log('Deleting orphaned user roles...')
      
      // Get all auth users
      const { data: authData, error: authError } = await adminClient.auth.admin.listUsers({
        page: 1,
        perPage: 1000
      })
      
      if (authError) {
        throw new Error(`Failed to fetch auth users: ${authError.message}`)
      }

      const authUserIds = new Set(authData.users.map(u => u.id))

      // Get all roles
      const { data: allRoles, error: rolesError } = await adminClient
        .from('user_roles')
        .select('id, role')
      
      if (rolesError) {
        throw new Error(`Failed to fetch roles: ${rolesError.message}`)
      }

      let orphanedRoles = allRoles.filter(r => !authUserIds.has(r.id))

      if (userIds && userIds.length > 0) {
        // Filter to specific user IDs if provided
        orphanedRoles = orphanedRoles.filter(r => userIds.includes(r.id))
      }

      results.processed = orphanedRoles.length

      for (const roleRecord of orphanedRoles) {
        try {
          const { error: deleteError } = await adminClient
            .from('user_roles')
            .delete()
            .eq('id', roleRecord.id)

          if (deleteError) {
            results.errors.push(`Failed to delete orphaned role ${roleRecord.id}: ${deleteError.message}`)
          } else {
            results.deleted++
            results.details.push(`Deleted orphaned role ${roleRecord.role} for ${roleRecord.id}`)
          }
        } catch (error) {
          results.errors.push(`Error deleting orphaned role ${roleRecord.id}: ${error.message}`)
        }
      }

    } else {
      return res.status(400).json({ 
        error: 'Invalid action', 
        message: 'Supported actions: create_missing_profiles, create_missing_roles, delete_orphaned_profiles, delete_orphaned_roles' 
      })
    }

    return res.status(200).json({
      success: true,
      message: `Cleanup completed: ${results.created} created, ${results.deleted} deleted, ${results.errors.length} errors`,
      results
    })

  } catch (error) {
    console.error('Error in user cleanup:', error)
    return res.status(500).json({ 
      error: 'Cleanup failed', 
      message: error.message 
    })
  }
}
