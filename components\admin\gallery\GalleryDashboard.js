import { useState, useEffect } from 'react';
import { authenticatedFetch } from '@/lib/auth-utils';
import { safeRender } from '@/lib/safe-render-utils';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/Dashboard.module.css'; // Reuse existing styles

export default function GalleryDashboard({ refreshKey, onMigrateData }) {
  const [stats, setStats] = useState({
    totalItems: 0,
    activeItems: 0,
    featuredItems: 0,
    totalCategories: 0,
    recentItems: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch dashboard statistics
  useEffect(() => {
    fetchStats();
  }, [refreshKey]);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch gallery items stats using authenticatedFetch
      const [itemsData, categoriesData] = await Promise.all([
        authenticatedFetch('/api/admin/gallery', {}, {
          redirect: false, // Don't redirect on auth failure, just show error
          notify: true     // Show notifications for errors
        }),
        authenticatedFetch('/api/admin/gallery/categories', {}, {
          redirect: false, // Don't redirect on auth failure, just show error
          notify: true     // Show notifications for errors
        })
      ]);

      const items = itemsData.data || [];
      const categories = categoriesData.data || [];

      // Calculate statistics
      const totalItems = items.length;
      const activeItems = items.filter(item => item.status === 'active').length;
      const featuredItems = items.filter(item => item.featured).length;
      const totalCategories = categories.length;

      // Get recent items (last 5)
      const recentItems = items
        .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
        .slice(0, 5);

      setStats({
        totalItems,
        activeItems,
        featuredItems,
        totalCategories,
        recentItems
      });
    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError(err.message);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error: {error}</p>
        <button onClick={fetchStats} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.welcomeSection}>
        <h2>Gallery Management Dashboard</h2>
        <p>Manage your gallery images, categories, and showcase your beautiful work.</p>
      </div>

      {/* Statistics Cards */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>🖼️</div>
          <div className={styles.statContent}>
            <h3>{stats.totalItems}</h3>
            <p>Total Gallery Items</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <h3>{stats.activeItems}</h3>
            <p>Active Items</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>⭐</div>
          <div className={styles.statContent}>
            <h3>{stats.featuredItems}</h3>
            <p>Featured Items</p>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>📁</div>
          <div className={styles.statContent}>
            <h3>{stats.totalCategories}</h3>
            <p>Categories</p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className={styles.quickActions}>
        <h3>Quick Actions</h3>
        <div className={styles.actionGrid}>
          <div className={styles.actionCard}>
            <h4>🆕 Add Gallery Item</h4>
            <p>Upload and showcase new artwork</p>
            <button
              className={styles.actionButton}
              onClick={() => window.location.href = '/admin/gallery?tab=gallery'}
            >
              Add Item
            </button>
          </div>

          <div className={styles.actionCard}>
            <h4>📂 Manage Categories</h4>
            <p>Organize your gallery with categories</p>
            <button
              className={styles.actionButton}
              onClick={() => window.location.href = '/admin/gallery?tab=categories'}
            >
              Manage Categories
            </button>
          </div>

          <div className={styles.actionCard}>
            <h4>🔄 Migrate Data</h4>
            <p>Import existing gallery data to database</p>
            <button
              className={styles.actionButton}
              onClick={onMigrateData}
            >
              Migrate Data
            </button>
          </div>

          <div className={styles.actionCard}>
            <h4>👁️ View Gallery</h4>
            <p>See how your gallery looks to visitors</p>
            <button
              className={styles.actionButton}
              onClick={() => window.open('/gallery', '_blank')}
            >
              View Gallery
            </button>
          </div>
        </div>
      </div>

      {/* Recent Items */}
      {stats.recentItems.length > 0 && (
        <div className={styles.recentSection}>
          <h3>Recent Gallery Items</h3>
          <div className={styles.recentItems}>
            {stats.recentItems.map((item) => (
              <div key={item.id} className={styles.recentItem}>
                <div className={styles.itemImage}>
                  <img
                    src={safeRender(item.main_image_url)}
                    alt={safeRender(item.title)}
                    onError={(e) => {
                      e.target.src = '/images/placeholder.svg';
                    }}
                  />
                </div>
                <div className={styles.itemInfo}>
                  <h4>{safeRender(item.title)}</h4>
                  <p className={styles.itemCategory}>
                    Category: {safeRender(item.category)}
                  </p>
                  <p className={styles.itemDate}>
                    Added: {new Date(item.created_at).toLocaleDateString()}
                  </p>
                  <div className={styles.itemBadges}>
                    <span className={`${styles.statusBadge} ${styles[item.status]}`}>
                      {safeRender(item.status)}
                    </span>
                    {item.featured && (
                      <span className={styles.featuredBadge}>⭐ Featured</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Getting Started */}
      {stats.totalItems === 0 && (
        <div className={styles.gettingStarted}>
          <h3>🚀 Getting Started</h3>
          <div className={styles.startedContent}>
            <p>Welcome to your Gallery Management system! Here's how to get started:</p>
            <ol>
              <li>
                <strong>Migrate Existing Data:</strong> If you have existing gallery images,
                click "Migrate Data" to import them into the database.
              </li>
              <li>
                <strong>Create Categories:</strong> Organize your gallery by creating categories
                like "Face Art", "Body Art", "UV Art", etc.
              </li>
              <li>
                <strong>Add Gallery Items:</strong> Upload your beautiful artwork and organize
                them into categories.
              </li>
              <li>
                <strong>Feature Your Best Work:</strong> Mark your best pieces as "Featured"
                to highlight them in the gallery.
              </li>
            </ol>

            <div className={styles.startedActions}>
              <button
                className={styles.primaryButton}
                onClick={onMigrateData}
              >
                🔄 Start with Data Migration
              </button>
              <button
                className={styles.secondaryButton}
                onClick={() => window.location.href = '/admin/gallery?tab=categories'}
              >
                📁 Create Categories First
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Tips */}
      <div className={styles.tipsSection}>
        <h3>💡 Tips for Gallery Management</h3>
        <div className={styles.tips}>
          <div className={styles.tip}>
            <h4>🎨 Image Quality</h4>
            <p>Use high-quality images (at least 1200px wide) for the best gallery experience.</p>
          </div>
          <div className={styles.tip}>
            <h4>📝 Descriptions</h4>
            <p>Add detailed descriptions to help visitors understand your artistic process.</p>
          </div>
          <div className={styles.tip}>
            <h4>🏷️ Categories</h4>
            <p>Use clear category names to help visitors find specific types of artwork.</p>
          </div>
          <div className={styles.tip}>
            <h4>⭐ Featured Items</h4>
            <p>Feature your best work to showcase your skills and attract more clients.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
