import { useState, useEffect } from 'react';
import authTokenManager from '@/lib/auth-token-manager';

/**
 * Debug page to test authentication functionality
 */
export default function AuthTest() {
  const [authInfo, setAuthInfo] = useState(null);
  const [apiTest, setApiTest] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    testAuthentication();
  }, []);

  const testAuthentication = async () => {
    try {
      setLoading(true);

      // Test frontend auth token manager
      const tokenFromStorage = authTokenManager.getTokenFromStorage();
      const authToken = await authTokenManager.getAuthToken().catch(e => null);

      const frontendAuth = {
        hasTokenInStorage: !!tokenFromStorage,
        tokenLength: tokenFromStorage?.length || 0,
        hasAuthToken: !!authToken,
        authTokenLength: authToken?.length || 0,
        tokenSample: tokenFromStorage ? `${tokenFromStorage.substring(0, 20)}...` : 'None'
      };

      setAuthInfo(frontendAuth);

      // Test API endpoint
      const token = tokenFromStorage || authToken;
      const response = await fetch('/api/debug/auth-test', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Content-Type': 'application/json'
        }
      });

      const apiResult = await response.json();
      setApiTest(apiResult);

    } catch (error) {
      console.error('Auth test error:', error);
      setApiTest({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testEventsAPI = async () => {
    try {
      const token = authTokenManager.getTokenFromStorage() || await authTokenManager.getAuthToken();
      
      const response = await fetch('/api/admin/events', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      console.log('Events API test result:', result);
      alert(`Events API test: ${response.ok ? 'SUCCESS' : 'FAILED'}\n${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      console.error('Events API test error:', error);
      alert(`Events API test ERROR: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '2rem', fontFamily: 'monospace' }}>
        <h1>Authentication Test</h1>
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '2rem', fontFamily: 'monospace', maxWidth: '800px' }}>
      <h1>Authentication Debug Page</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Frontend Authentication</h2>
        <pre style={{ background: '#f5f5f5', padding: '1rem', borderRadius: '4px' }}>
          {JSON.stringify(authInfo, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>API Authentication Test</h2>
        <pre style={{ background: '#f5f5f5', padding: '1rem', borderRadius: '4px' }}>
          {JSON.stringify(apiTest, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Test Actions</h2>
        <button 
          onClick={testAuthentication}
          style={{ 
            padding: '0.5rem 1rem', 
            marginRight: '1rem',
            background: '#4ECDC4',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Auth Test
        </button>
        
        <button 
          onClick={testEventsAPI}
          style={{ 
            padding: '0.5rem 1rem',
            background: '#667eea',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Test Events API
        </button>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Environment Info</h2>
        <ul>
          <li>NODE_ENV: {process.env.NODE_ENV}</li>
          <li>Auth Bypass: {process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS}</li>
          <li>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Server-side'}</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Quick Fixes</h2>
        <p>If you're seeing authentication errors, try these steps:</p>
        <ol>
          <li>Check that ENABLE_AUTH_BYPASS=true is in your .env.local file</li>
          <li>Restart your development server after changing .env.local</li>
          <li>Clear browser localStorage and cookies</li>
          <li>Check browser console for JavaScript errors</li>
        </ol>
      </div>

      <div style={{ marginTop: '2rem', padding: '1rem', background: '#fff3cd', borderRadius: '4px' }}>
        <h3>Navigation</h3>
        <a href="/admin/events" style={{ color: '#4ECDC4', textDecoration: 'none' }}>
          → Go to Admin Events Page
        </a>
      </div>
    </div>
  );
}
