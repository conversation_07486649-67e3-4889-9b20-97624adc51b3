-- Fix POS booking constraints and database issues
-- This migration addresses critical issues preventing POS bookings from being created

-- =============================================
-- FIX CUSTOMERS TABLE CONSTRAINTS
-- =============================================

-- Remove NOT NULL constraint from customers.email to allow anonymous POS customers
ALTER TABLE public.customers 
ALTER COLUMN email DROP NOT NULL;

-- Add a unique constraint that allows multiple NULL emails but prevents duplicate non-NULL emails
DROP INDEX IF EXISTS customers_email_key;
CREATE UNIQUE INDEX customers_email_unique_idx ON public.customers (email) WHERE email IS NOT NULL;

-- =============================================
-- ENHANCE CUSTOMERS TABLE FOR POS
-- =============================================

-- Add POS-specific fields to customers table
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS is_anonymous BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS pos_customer BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS customer_type TEXT DEFAULT 'regular' CHECK (customer_type IN ('regular', 'anonymous', 'walk_in', 'vip'));

-- =============================================
-- ENHANCE BOOKINGS TABLE FOR POS
-- =============================================

-- Add POS-specific fields to bookings table
ALTER TABLE public.bookings 
ADD COLUMN IF NOT EXISTS booking_source TEXT DEFAULT 'admin' CHECK (booking_source IN ('admin', 'pos', 'online', 'phone', 'walk_in')),
ADD COLUMN IF NOT EXISTS pos_session_id TEXT,
ADD COLUMN IF NOT EXISTS tier_name TEXT,
ADD COLUMN IF NOT EXISTS tier_price DECIMAL(10, 2);

-- =============================================
-- ENHANCE PAYMENTS TABLE FOR POS
-- =============================================

-- Add POS-specific fields to payments table
ALTER TABLE public.payments 
ADD COLUMN IF NOT EXISTS pos_session_id TEXT,
ADD COLUMN IF NOT EXISTS square_payment_id TEXT,
ADD COLUMN IF NOT EXISTS receipt_url TEXT;

-- =============================================
-- CREATE POS SESSIONS TABLE
-- =============================================

-- Create table to track POS sessions for debugging and analytics
CREATE TABLE IF NOT EXISTS public.pos_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  session_id TEXT NOT NULL UNIQUE,
  user_id UUID REFERENCES auth.users(id),
  start_time TIMESTAMPTZ DEFAULT NOW(),
  end_time TIMESTAMPTZ,
  total_bookings INTEGER DEFAULT 0,
  total_revenue DECIMAL(10, 2) DEFAULT 0,
  payment_methods_used TEXT[],
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'abandoned')),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for POS-related queries
CREATE INDEX IF NOT EXISTS customers_pos_customer_idx ON public.customers(pos_customer);
CREATE INDEX IF NOT EXISTS customers_is_anonymous_idx ON public.customers(is_anonymous);
CREATE INDEX IF NOT EXISTS bookings_booking_source_idx ON public.bookings(booking_source);
CREATE INDEX IF NOT EXISTS bookings_pos_session_id_idx ON public.bookings(pos_session_id);
CREATE INDEX IF NOT EXISTS payments_pos_session_id_idx ON public.payments(pos_session_id);
CREATE INDEX IF NOT EXISTS payments_square_payment_id_idx ON public.payments(square_payment_id);

-- =============================================
-- UPDATE EXISTING DATA
-- =============================================

-- Mark existing customers with emails as regular customers
UPDATE public.customers 
SET customer_type = 'regular', pos_customer = FALSE, is_anonymous = FALSE
WHERE email IS NOT NULL;

-- =============================================
-- CREATE HELPER FUNCTIONS
-- =============================================

-- Function to generate anonymous customer names
CREATE OR REPLACE FUNCTION generate_anonymous_customer_name()
RETURNS TEXT AS $$
BEGIN
  RETURN 'Walk-in Customer #' || EXTRACT(EPOCH FROM NOW())::INTEGER;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old anonymous customers (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_anonymous_customers()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.customers 
  WHERE is_anonymous = TRUE 
    AND created_at < NOW() - INTERVAL '30 days'
    AND id NOT IN (
      SELECT DISTINCT customer_id 
      FROM public.bookings 
      WHERE customer_id IS NOT NULL
    );
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- ENABLE ROW LEVEL SECURITY POLICIES
-- =============================================

-- Ensure RLS is enabled on new tables
ALTER TABLE public.pos_sessions ENABLE ROW LEVEL SECURITY;

-- Create policies for POS sessions
CREATE POLICY "Staff can view all POS sessions" ON public.pos_sessions
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can create POS sessions" ON public.pos_sessions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can update their own POS sessions" ON public.pos_sessions
  FOR UPDATE USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.user_roles 
      WHERE id = auth.uid() AND role IN ('admin', 'staff')
    )
  );

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Verify the changes
DO $$
BEGIN
  -- Check if email constraint was removed
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE table_name = 'customers' 
      AND constraint_type = 'NOT NULL' 
      AND column_name = 'email'
  ) THEN
    RAISE NOTICE 'SUCCESS: Email constraint removed from customers table';
  ELSE
    RAISE WARNING 'ISSUE: Email constraint still exists on customers table';
  END IF;
  
  -- Check if new columns were added
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'customers' AND column_name = 'is_anonymous'
  ) THEN
    RAISE NOTICE 'SUCCESS: is_anonymous column added to customers table';
  ELSE
    RAISE WARNING 'ISSUE: is_anonymous column not found in customers table';
  END IF;
  
  -- Check if POS sessions table was created
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'pos_sessions'
  ) THEN
    RAISE NOTICE 'SUCCESS: pos_sessions table created';
  ELSE
    RAISE WARNING 'ISSUE: pos_sessions table not created';
  END IF;
END $$;
