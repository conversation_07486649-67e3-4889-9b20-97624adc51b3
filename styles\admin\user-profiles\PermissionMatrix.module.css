.permissionMatrix {
  padding: 30px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.headerContent h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
}

.headerContent p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.headerActions {
  display: flex;
  gap: 12px;
}

.resetButton,
.saveButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.resetButton {
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #e9ecef;
}

.resetButton:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.saveButton {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: 2px solid transparent;
}

.saveButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.saveButton:disabled,
.resetButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.matrixContainer {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.roleHeaders {
  display: grid;
  grid-template-columns: 300px repeat(5, 1fr);
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  position: sticky;
  top: 0;
  z-index: 5;
}

.permissionHeader {
  padding: 20px;
  font-weight: 600;
  color: #333;
  font-size: 1rem;
  border-right: 1px solid #e9ecef;
}

.roleHeader {
  padding: 20px 15px;
  text-align: center;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.roleIcon {
  font-size: 1.5rem;
}

.roleName {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.categorySection {
  border-bottom: 1px solid #f0f0f0;
}

.categoryHeader {
  background: #f8f9fa;
  padding: 15px 20px;
  border-left: 4px solid #6e8efb;
  border-bottom: 1px solid #e9ecef;
}

.categoryHeader h3 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: capitalize;
}

.permissionRow {
  display: grid;
  grid-template-columns: 300px repeat(5, 1fr);
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.permissionRow:hover {
  background: #f8f9ff;
}

.permissionInfo {
  padding: 20px;
  border-right: 1px solid #f0f0f0;
}

.permissionInfo h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 0.95rem;
  font-weight: 600;
}

.permissionInfo p {
  margin: 0;
  color: #666;
  font-size: 0.85rem;
  line-height: 1.4;
}

.permissionCell {
  padding: 20px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #f0f0f0;
}

.checkboxLabel {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkboxLabel input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkboxCustom {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 4px;
  position: relative;
  transition: all 0.2s ease;
}

.checkboxLabel input[type="checkbox"]:checked + .checkboxCustom {
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  border-color: #6e8efb;
}

.checkboxLabel input[type="checkbox"]:checked + .checkboxCustom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkboxLabel input[type="checkbox"]:disabled + .checkboxCustom {
  background: #f8f9fa;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.checkboxLabel input[type="checkbox"]:disabled:checked + .checkboxCustom {
  background: #6c757d;
  border-color: #6c757d;
}

.legend {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.legend h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.legendItems {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.legendIcon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .roleHeaders {
    grid-template-columns: 250px repeat(5, 1fr);
  }
  
  .permissionRow {
    grid-template-columns: 250px repeat(5, 1fr);
  }
  
  .permissionInfo {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .permissionMatrix {
    padding: 20px;
  }
  
  .header {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }
  
  .matrixContainer {
    overflow-x: auto;
  }
  
  .roleHeaders {
    min-width: 800px;
  }
  
  .permissionRow {
    min-width: 800px;
  }
  
  .legendItems {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .permissionMatrix {
    padding: 15px;
  }
  
  .headerActions {
    flex-direction: column;
    width: 100%;
  }
  
  .resetButton,
  .saveButton {
    width: 100%;
  }
}
