/**
 * Performance Monitoring Utilities
 * Tracks image loading performance and identifies bottlenecks
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      imageLoading: new Map(),
      pageLoad: {},
      errors: []
    };
    this.startTime = Date.now();
  }

  /**
   * Start tracking image loading
   * @param {string} imageId - Unique identifier for the image
   * @param {string} src - Image source URL
   */
  startImageLoad(imageId, src) {
    this.metrics.imageLoading.set(imageId, {
      src,
      startTime: Date.now(),
      status: 'loading'
    });
  }

  /**
   * Mark image as loaded successfully
   * @param {string} imageId - Image identifier
   * @param {Object} dimensions - Image dimensions
   */
  imageLoaded(imageId, dimensions = {}) {
    const metric = this.metrics.imageLoading.get(imageId);
    if (metric) {
      metric.endTime = Date.now();
      metric.loadTime = metric.endTime - metric.startTime;
      metric.status = 'loaded';
      metric.dimensions = dimensions;
    }
  }

  /**
   * Mark image as failed to load
   * @param {string} imageId - Image identifier
   * @param {string} error - Error message
   */
  imageError(imageId, error) {
    const metric = this.metrics.imageLoading.get(imageId);
    if (metric) {
      metric.endTime = Date.now();
      metric.loadTime = metric.endTime - metric.startTime;
      metric.status = 'error';
      metric.error = error;
    }

    this.metrics.errors.push({
      type: 'image_load_error',
      imageId,
      error,
      timestamp: Date.now()
    });
  }

  /**
   * Get loading statistics
   * @returns {Object} - Loading statistics
   */
  getStats() {
    const images = Array.from(this.metrics.imageLoading.values());
    const loaded = images.filter(img => img.status === 'loaded');
    const errors = images.filter(img => img.status === 'error');
    const loading = images.filter(img => img.status === 'loading');

    const loadTimes = loaded.map(img => img.loadTime).filter(time => time > 0);
    const avgLoadTime = loadTimes.length > 0 ? loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length : 0;
    const maxLoadTime = loadTimes.length > 0 ? Math.max(...loadTimes) : 0;
    const minLoadTime = loadTimes.length > 0 ? Math.min(...loadTimes) : 0;

    return {
      total: images.length,
      loaded: loaded.length,
      errors: errors.length,
      loading: loading.length,
      successRate: images.length > 0 ? (loaded.length / images.length * 100).toFixed(1) : 0,
      avgLoadTime: Math.round(avgLoadTime),
      maxLoadTime,
      minLoadTime,
      totalErrors: this.metrics.errors.length
    };
  }

  /**
   * Get slow loading images (over threshold)
   * @param {number} threshold - Threshold in milliseconds (default: 3000ms)
   * @returns {Array} - Array of slow loading images
   */
  getSlowImages(threshold = 3000) {
    return Array.from(this.metrics.imageLoading.values())
      .filter(img => img.status === 'loaded' && img.loadTime > threshold)
      .sort((a, b) => b.loadTime - a.loadTime);
  }

  /**
   * Get failed images
   * @returns {Array} - Array of failed images
   */
  getFailedImages() {
    return Array.from(this.metrics.imageLoading.values())
      .filter(img => img.status === 'error');
  }

  /**
   * Export performance report
   * @returns {Object} - Comprehensive performance report
   */
  getReport() {
    const stats = this.getStats();
    const slowImages = this.getSlowImages();
    const failedImages = this.getFailedImages();

    return {
      timestamp: new Date().toISOString(),
      sessionDuration: Date.now() - this.startTime,
      browser: this.getBrowserInfo(),
      stats,
      slowImages: slowImages.slice(0, 10), // Top 10 slowest
      failedImages,
      errors: this.metrics.errors,
      recommendations: this.getRecommendations(stats, slowImages, failedImages)
    };
  }

  /**
   * Get browser information
   * @returns {Object} - Browser information
   */
  getBrowserInfo() {
    if (typeof window === 'undefined') return {};

    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      deviceMemory: navigator.deviceMemory || 'unknown',
      hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
      connection: connection ? {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      } : null
    };
  }

  /**
   * Generate performance recommendations
   * @param {Object} stats - Performance statistics
   * @param {Array} slowImages - Slow loading images
   * @param {Array} failedImages - Failed images
   * @returns {Array} - Array of recommendations
   */
  getRecommendations(stats, slowImages, failedImages) {
    const recommendations = [];

    if (stats.successRate < 90) {
      recommendations.push({
        type: 'error_rate',
        severity: 'high',
        message: `High error rate: ${100 - stats.successRate}% of images failed to load`,
        action: 'Check image URLs and server availability'
      });
    }

    if (stats.avgLoadTime > 2000) {
      recommendations.push({
        type: 'slow_loading',
        severity: 'medium',
        message: `Average load time is ${stats.avgLoadTime}ms`,
        action: 'Consider image optimization, compression, or CDN usage'
      });
    }

    if (slowImages.length > 5) {
      recommendations.push({
        type: 'many_slow_images',
        severity: 'medium',
        message: `${slowImages.length} images are loading slowly (>3s)`,
        action: 'Implement progressive loading or reduce image sizes'
      });
    }

    if (failedImages.length > 0) {
      recommendations.push({
        type: 'failed_images',
        severity: 'high',
        message: `${failedImages.length} images failed to load`,
        action: 'Check image paths and implement fallback images'
      });
    }

    if (stats.total > 20) {
      recommendations.push({
        type: 'many_images',
        severity: 'low',
        message: `Loading ${stats.total} images on one page`,
        action: 'Consider pagination or lazy loading strategies'
      });
    }

    return recommendations;
  }

  /**
   * Log performance data to console (development only)
   */
  logToConsole() {
    if (process.env.NODE_ENV !== 'development') return;

    const report = this.getReport();
    console.group('🖼️ Image Loading Performance Report');
    console.log('📊 Statistics:', report.stats);
    
    if (report.slowImages.length > 0) {
      console.log('🐌 Slow Images:', report.slowImages);
    }
    
    if (report.failedImages.length > 0) {
      console.log('❌ Failed Images:', report.failedImages);
    }
    
    if (report.recommendations.length > 0) {
      console.log('💡 Recommendations:', report.recommendations);
    }
    
    console.groupEnd();
  }

  /**
   * Reset all metrics
   */
  reset() {
    this.metrics = {
      imageLoading: new Map(),
      pageLoad: {},
      errors: []
    };
    this.startTime = Date.now();
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

// Auto-log performance data every 30 seconds in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const stats = performanceMonitor.getStats();
    if (stats.total > 0) {
      performanceMonitor.logToConsole();
    }
  }, 30000);
}

export default performanceMonitor;

/**
 * React hook for performance monitoring
 * @param {string} componentName - Name of the component
 * @returns {Object} - Performance monitoring functions
 */
export const usePerformanceMonitor = (componentName = 'unknown') => {
  const trackImageLoad = (imageId, src) => {
    performanceMonitor.startImageLoad(`${componentName}-${imageId}`, src);
  };

  const imageLoaded = (imageId, dimensions) => {
    performanceMonitor.imageLoaded(`${componentName}-${imageId}`, dimensions);
  };

  const imageError = (imageId, error) => {
    performanceMonitor.imageError(`${componentName}-${imageId}`, error);
  };

  const getStats = () => performanceMonitor.getStats();

  return {
    trackImageLoad,
    imageLoaded,
    imageError,
    getStats
  };
};
