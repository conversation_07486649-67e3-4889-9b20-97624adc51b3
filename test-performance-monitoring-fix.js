#!/usr/bin/env node

/**
 * Test Script for Square Performance Monitoring Fix
 * Verifies that the browser compatibility issues are resolved
 */

import { launch } from 'puppeteer';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_TIMEOUT = 30000;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logResult(test, status, details = '') {
  const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
  const color = status === 'pass' ? 'green' : status === 'fail' ? 'red' : 'yellow';
  log(`${icon} ${test}: ${details}`, color);
}

// Test performance monitoring fix
async function testPerformanceMonitoringFix() {
  log('\n🧪 Testing Performance Monitoring Fix...', 'blue');
  
  let browser;
  try {
    browser = await launch({ 
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Track console messages and errors
    const consoleMessages = [];
    const jsErrors = [];
    
    page.on('console', msg => {
      const message = msg.text();
      consoleMessages.push({
        type: msg.type(),
        text: message,
        timestamp: Date.now()
      });
    });
    
    page.on('pageerror', error => {
      jsErrors.push({
        message: error.message,
        stack: error.stack,
        timestamp: Date.now()
      });
    });
    
    // Navigate to POS page
    log('Navigating to POS page...', 'blue');
    await page.goto(`${BASE_URL}/admin/pos`, { 
      waitUntil: 'networkidle0',
      timeout: TEST_TIMEOUT 
    });
    
    // Wait for scripts to load
    await page.waitForTimeout(3000);
    
    // Check for JavaScript errors related to process.env
    const processEnvErrors = jsErrors.filter(error => 
      error.message.includes('process is not defined') ||
      error.message.includes('process.env')
    );
    
    if (processEnvErrors.length === 0) {
      logResult('Process.env Fix', 'pass', 'No process.env errors detected');
    } else {
      logResult('Process.env Fix', 'fail', `${processEnvErrors.length} process.env errors found`);
      processEnvErrors.forEach(error => 
        log(`  - ${error.message}`, 'red')
      );
    }
    
    // Test performance monitoring functionality
    const performanceMonitoringTest = await page.evaluate(() => {
      try {
        // Check if performance monitoring functions are available
        const hasGetReport = typeof window.getSquarePerformanceReport === 'function';
        const hasClearMetrics = typeof window.clearSquarePerformanceMetrics === 'function';
        const hasTestFunction = typeof window.testSquarePerformanceMonitoring === 'function';
        
        if (!hasGetReport || !hasClearMetrics || !hasTestFunction) {
          return {
            success: false,
            error: 'Performance monitoring functions not available',
            functions: { hasGetReport, hasClearMetrics, hasTestFunction }
          };
        }
        
        // Test the monitoring system
        const testResult = window.testSquarePerformanceMonitoring();
        
        return {
          success: true,
          testResult,
          functions: { hasGetReport, hasClearMetrics, hasTestFunction }
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });
    
    if (performanceMonitoringTest.success) {
      logResult('Performance Monitoring', 'pass', 'All functions working correctly');
      log(`  - Test violations recorded: ${performanceMonitoringTest.testResult.totalViolations}`, 'green');
    } else {
      logResult('Performance Monitoring', 'fail', performanceMonitoringTest.error);
      if (performanceMonitoringTest.functions) {
        log(`  - Functions available: ${JSON.stringify(performanceMonitoringTest.functions)}`, 'yellow');
      }
    }
    
    // Test environment detection
    const environmentTest = await page.evaluate(() => {
      try {
        // This should work without errors now
        const testResult = window.testSquarePerformanceMonitoring();
        return {
          success: true,
          isDevelopment: testResult ? true : false
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });
    
    if (environmentTest.success) {
      logResult('Environment Detection', 'pass', 'Environment detection working correctly');
    } else {
      logResult('Environment Detection', 'fail', environmentTest.error);
    }
    
    // Test extension error suppression
    const extensionErrorTest = await page.evaluate(() => {
      try {
        // Check if extension error suppression functions are available
        const hasDebugFunction = typeof window.debugExtensionErrors === 'function';
        const hasRestoreFunction = typeof window.restoreConsole === 'function';
        
        return {
          success: true,
          functions: { hasDebugFunction, hasRestoreFunction }
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });
    
    if (extensionErrorTest.success) {
      logResult('Extension Error Suppression', 'pass', 'Error suppression functions available');
    } else {
      logResult('Extension Error Suppression', 'fail', extensionErrorTest.error);
    }
    
    // Check for runtime.lastError messages that should be suppressed
    const runtimeErrors = consoleMessages.filter(msg => 
      msg.text.includes('runtime.lastError') || 
      msg.text.includes('message port closed')
    );
    
    const suppressedErrors = consoleMessages.filter(msg => 
      msg.text.includes('[SUPPRESSED') && 
      (msg.text.includes('runtime.lastError') || msg.text.includes('message port closed'))
    );
    
    const visibleRuntimeErrors = runtimeErrors.filter(error => 
      !suppressedErrors.some(suppressed => suppressed.text.includes(error.text))
    );
    
    if (visibleRuntimeErrors.length === 0) {
      logResult('Runtime Error Suppression', 'pass', 
        `${suppressedErrors.length} runtime errors successfully suppressed`);
    } else {
      logResult('Runtime Error Suppression', 'warn', 
        `${visibleRuntimeErrors.length} runtime errors still visible`);
      visibleRuntimeErrors.slice(0, 3).forEach(error => 
        log(`  - ${error.text}`, 'yellow')
      );
    }
    
    // Test Square payment flow to trigger performance monitoring
    log('Testing Square payment flow...', 'blue');
    
    try {
      // Navigate through the payment flow
      await page.waitForSelector('[data-testid="service-card"], .service-card, .pos-service-item', { timeout: 10000 });
      const serviceCards = await page.$$('[data-testid="service-card"], .service-card, .pos-service-item');
      
      if (serviceCards.length > 0) {
        await serviceCards[0].click();
        await page.waitForTimeout(1000);
        
        // Select a tier
        const tierButtons = await page.$$('[data-testid="tier-button"], .tier-button, .pos-tier-item');
        if (tierButtons.length > 0) {
          await tierButtons[0].click();
          await page.waitForTimeout(1000);
          
          // Skip customer info
          const skipButton = await page.$('[data-testid="skip-customer"], .skip-button');
          const nextButton = await page.$('[data-testid="next-button"], .next-button, button[type="submit"]');
          
          if (skipButton) {
            await skipButton.click();
          } else if (nextButton) {
            await nextButton.click();
          }
          
          await page.waitForTimeout(1000);
          
          // Select card payment
          const cardPaymentButton = await page.$('[data-testid="card-payment"], .card-payment-button, button:contains("Card")');
          if (cardPaymentButton) {
            await cardPaymentButton.click();
            await page.waitForTimeout(5000);
            
            // Get performance report after Square initialization
            const finalReport = await page.evaluate(() => {
              if (typeof window.getSquarePerformanceReport === 'function') {
                return window.getSquarePerformanceReport();
              }
              return null;
            });
            
            if (finalReport && finalReport.totalViolations > 0) {
              logResult('Square Flow Performance', 'pass', 
                `${finalReport.totalViolations} violations recorded during Square initialization`);
            } else {
              logResult('Square Flow Performance', 'warn', 'No performance violations recorded');
            }
          }
        }
      }
    } catch (flowError) {
      logResult('Square Flow Test', 'warn', `Flow test incomplete: ${flowError.message}`);
    }
    
    // Final summary
    log('\n📊 Test Summary', 'bold');
    log('================', 'blue');
    
    const totalJSErrors = jsErrors.length;
    const processErrors = processEnvErrors.length;
    
    if (processErrors === 0 && performanceMonitoringTest.success) {
      log('🎉 All critical fixes verified! Performance monitoring is working correctly.', 'green');
    } else if (processErrors === 0) {
      log('✅ Process.env errors fixed, but performance monitoring needs attention.', 'yellow');
    } else {
      log(`❌ ${processErrors} critical errors still present.`, 'red');
    }
    
    log(`\nTotal JavaScript errors: ${totalJSErrors}`);
    log(`Process.env errors: ${processErrors}`);
    log(`Performance monitoring: ${performanceMonitoringTest.success ? 'Working' : 'Failed'}`);
    log(`Extension error suppression: ${extensionErrorTest.success ? 'Working' : 'Failed'}`);
    
  } catch (error) {
    logResult('Test Execution', 'fail', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Main test runner
async function runFixVerificationTests() {
  log('🧪 Square Performance Monitoring Fix Verification', 'bold');
  log('=================================================', 'blue');
  
  // Check if development server is running
  try {
    const response = await fetch(BASE_URL);
    if (!response.ok) {
      throw new Error(`Server returned ${response.status}`);
    }
    logResult('Development Server', 'pass', 'Server is running');
  } catch (error) {
    logResult('Development Server', 'fail', 'Server not accessible. Please run: npm run dev');
    return;
  }
  
  await testPerformanceMonitoringFix();
  
  log('\n💡 Next Steps:', 'blue');
  log('1. If all tests pass, the performance monitoring fix is complete');
  log('2. Monitor console for any remaining process.env errors');
  log('3. Verify Square payment processing continues to work normally');
  log('4. Test in multiple browsers to ensure compatibility');
}

// Run the tests
runFixVerificationTests().catch(console.error);
