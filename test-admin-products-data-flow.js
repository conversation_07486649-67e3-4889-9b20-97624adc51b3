/**
 * Test script to verify admin products data flow
 * Tests: admin interface → database → public shop page
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testProductsDataFlow() {
  console.log('🔍 Testing Products Data Flow...\n');

  try {
    // Test 1: Check admin API endpoint
    console.log('1. Testing Admin Products API...');
    const adminResponse = await fetch('http://localhost:3001/api/admin/inventory/products', {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (adminResponse.ok) {
      const adminData = await adminResponse.json();
      console.log(`✅ Admin API: Found ${adminData.products?.length || 0} products`);
      
      // Show first few products
      if (adminData.products?.length > 0) {
        console.log('   Sample products:');
        adminData.products.slice(0, 3).forEach(product => {
          console.log(`   - ${product.name} (${product.category || 'no category'})`);
        });
      }
    } else {
      console.log(`❌ Admin API failed: ${adminResponse.status}`);
    }

    // Test 2: Check public API endpoint
    console.log('\n2. Testing Public Products API...');
    const publicResponse = await fetch('http://localhost:3001/api/public/products');

    if (publicResponse.ok) {
      const publicData = await publicResponse.json();
      console.log(`✅ Public API: Found ${publicData.products?.length || 0} products`);
      
      // Show first few products
      if (publicData.products?.length > 0) {
        console.log('   Sample products:');
        publicData.products.slice(0, 3).forEach(product => {
          console.log(`   - ${product.name} (${product.category || 'no category'})`);
          console.log(`     Image: ${product.image || 'no image'}`);
          console.log(`     Description: ${product.description?.substring(0, 80) || 'no description'}...`);
        });
      }
    } else {
      console.log(`❌ Public API failed: ${publicResponse.status}`);
    }

    // Test 3: Check database directly
    console.log('\n3. Testing Database Direct Access...');
    const { data: dbProducts, error } = await supabase
      .from('products')
      .select('name, category_name, image_url, description, status')
      .eq('status', 'active')
      .limit(5);

    if (error) {
      console.log(`❌ Database error: ${error.message}`);
    } else {
      console.log(`✅ Database: Found ${dbProducts?.length || 0} active products`);
      
      if (dbProducts?.length > 0) {
        console.log('   Sample products:');
        dbProducts.forEach(product => {
          console.log(`   - ${product.name} (${product.category_name || 'no category'})`);
          console.log(`     Image: ${product.image_url || 'no image'}`);
          console.log(`     Description: ${product.description?.substring(0, 80) || 'no description'}...`);
        });
      }
    }

    // Test 4: Check category consistency
    console.log('\n4. Testing Category Consistency...');
    const { data: categoryData, error: categoryError } = await supabase
      .from('products')
      .select('category_name')
      .eq('status', 'active')
      .not('category_name', 'is', null);

    if (categoryError) {
      console.log(`❌ Category check error: ${categoryError.message}`);
    } else {
      const categories = [...new Set(categoryData.map(p => p.category_name))];
      console.log(`✅ Categories found: ${categories.join(', ')}`);
    }

    // Test 5: Check image URLs
    console.log('\n5. Testing Image URL Consistency...');
    const { data: imageData, error: imageError } = await supabase
      .from('products')
      .select('name, image_url')
      .eq('status', 'active');

    if (imageError) {
      console.log(`❌ Image check error: ${imageError.message}`);
    } else {
      const withImages = imageData.filter(p => p.image_url && p.image_url.startsWith('/images/products/'));
      const withoutImages = imageData.filter(p => !p.image_url || !p.image_url.startsWith('/images/products/'));
      
      console.log(`✅ Products with proper images: ${withImages.length}`);
      if (withoutImages.length > 0) {
        console.log(`⚠️  Products missing images: ${withoutImages.length}`);
        withoutImages.forEach(p => {
          console.log(`   - ${p.name}: ${p.image_url || 'null'}`);
        });
      }
    }

    console.log('\n🎉 Data flow test completed!');

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testProductsDataFlow();
}

module.exports = { testProductsDataFlow };
