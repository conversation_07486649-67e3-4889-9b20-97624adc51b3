.overview {
  padding: 30px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #666;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #6e8efb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.statCard {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s ease;
  border: 1px solid #f0f0f0;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.statIcon {
  font-size: 2.5rem;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.statContent h3 {
  font-size: 2.2rem;
  margin: 0;
  color: #333;
  font-weight: 700;
}

.statContent p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 0.95rem;
  font-weight: 500;
}

.section {
  margin-bottom: 40px;
}

.section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.roleDistribution {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.roleCard {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.roleCard:hover {
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.roleIcon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 15px;
}

.roleInfo h3 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.roleInfo p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 0.9rem;
}

.roleProgress {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.roleProgressBar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.profileList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.emptyState {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.emptyIcon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.emptyState h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.3rem;
}

.emptyState p {
  margin: 0;
  font-size: 1rem;
}

.profileCard {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.profileCard:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.profileHeader {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.profileAvatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.profileInfo h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
}

.profileInfo p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 0.9rem;
}

.roleBadge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: capitalize;
}

.profileStats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.profileStat {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.statLabel {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.statValue {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
}

.progressContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progressBar {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 0.8rem;
  color: #666;
  font-weight: 600;
  min-width: 35px;
}

.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.actionButton {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  text-align: left;
}

.actionButton:hover {
  border-color: #6e8efb;
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.15);
}

.actionIcon {
  font-size: 2rem;
  flex-shrink: 0;
}

.actionContent h4 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.actionContent p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .overview {
    padding: 20px;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
  }
  
  .roleDistribution {
    grid-template-columns: 1fr;
  }
  
  .profileList {
    grid-template-columns: 1fr;
  }
  
  .quickActions {
    grid-template-columns: 1fr;
  }
  
  .profileStats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .overview {
    padding: 15px;
  }
  
  .statCard {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .profileHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
