/* Admin-specific global styles */

/* Admin color variables */
:root {
  --admin-primary: #6a0dad;
  --admin-primary-light: #8e24aa;
  --admin-primary-dark: #4a0072;
  --admin-secondary: #2c3e50;
  --admin-secondary-light: #34495e;
  --admin-secondary-dark: #1a2530;

  --admin-success: #4caf50;
  --admin-warning: #ff9800;
  --admin-error: #f44336;
  --admin-info: #2196f3;

  --admin-text: #333333;
  --admin-text-light: #666666;
  --admin-text-lighter: #999999;
  --admin-text-inverse: #ffffff;

  --admin-bg: #f5f7fa;
  --admin-bg-light: #ffffff;
  --admin-bg-dark: #e9ecef;

  --admin-border: #e0e0e0;
  --admin-border-light: #f0f0f0;
  --admin-border-dark: #cccccc;

  --admin-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 2px 8px rgba(0, 0, 0, 0.1);
  --admin-shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);

  --admin-radius-sm: 4px;
  --admin-radius-md: 6px;
  --admin-radius-lg: 8px;
  --admin-radius-xl: 12px;

  --admin-spacing-xs: 4px;
  --admin-spacing-sm: 8px;
  --admin-spacing-md: 12px;
  --admin-spacing-lg: 16px;
  --admin-spacing-xl: 20px;
  --admin-spacing-2xl: 24px;
}

/* Optimized Layout Utilities */
.admin-viewport-container {
  height: 100vh;
  overflow: hidden;
  display: grid;
  grid-template-rows: auto 1fr;
}

.admin-content-area {
  overflow-y: auto;
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
}

.admin-grid-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--admin-spacing-md);
}

.admin-flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--admin-spacing-md);
}

.admin-card {
  background: var(--admin-bg-light);
  border-radius: var(--admin-radius-md);
  box-shadow: var(--admin-shadow-md);
  padding: var(--admin-spacing-lg);
  border: 1px solid var(--admin-border-light);
}

.admin-card-compact {
  background: var(--admin-bg-light);
  border-radius: var(--admin-radius-sm);
  box-shadow: var(--admin-shadow-sm);
  padding: var(--admin-spacing-md);
  border: 1px solid var(--admin-border-light);
}

/* Admin button styles */
.admin-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  border-radius: 0.25rem;
  transition: all 0.2s ease-in-out;
}

.admin-button:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

.admin-button:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.admin-button svg {
  margin-right: 0.5rem;
}

.admin-button--primary {
  background-color: var(--admin-primary);
  color: white;
  border-color: var(--admin-primary);
}

.admin-button--primary:hover {
  background-color: var(--admin-primary-dark);
  border-color: var(--admin-primary-dark);
}

.admin-button--secondary {
  background-color: var(--admin-secondary);
  color: white;
  border-color: var(--admin-secondary);
}

.admin-button--secondary:hover {
  background-color: var(--admin-secondary-dark);
  border-color: var(--admin-secondary-dark);
}

.admin-button--success {
  background-color: var(--admin-success);
  color: white;
  border-color: var(--admin-success);
}

.admin-button--success:hover {
  background-color: #3d8b40;
  border-color: #3d8b40;
}

.admin-button--danger {
  background-color: var(--admin-error);
  color: white;
  border-color: var(--admin-error);
}

.admin-button--danger:hover {
  background-color: #d32f2f;
  border-color: #d32f2f;
}

.admin-button--outline-primary {
  background-color: transparent;
  color: var(--admin-primary);
  border-color: var(--admin-primary);
}

.admin-button--outline-primary:hover {
  background-color: var(--admin-primary);
  color: white;
}

.admin-button--outline-secondary {
  background-color: transparent;
  color: var(--admin-secondary);
  border-color: var(--admin-secondary);
}

.admin-button--outline-secondary:hover {
  background-color: var(--admin-secondary);
  color: white;
}

.admin-button--sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.admin-button--lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Admin form styles */
.admin-form-group {
  margin-bottom: 1rem;
}

.admin-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--admin-text);
}

.admin-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--admin-text);
  background-color: white;
  background-clip: padding-box;
  border: 1px solid var(--admin-border);
  border-radius: 0.25rem;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.admin-form-control:focus {
  border-color: var(--admin-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.25);
}

.admin-form-control:disabled {
  background-color: var(--admin-bg-dark);
  opacity: 1;
}

/* Admin table styles */
.admin-table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--admin-text);
  border-collapse: collapse;
}

.admin-table th,
.admin-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--admin-border);
}

.admin-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--admin-border);
  background-color: var(--admin-bg-dark);
  color: var(--admin-text-light);
  font-weight: 500;
}

.admin-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.admin-table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.admin-table-bordered {
  border: 1px solid var(--admin-border);
}

.admin-table-bordered th,
.admin-table-bordered td {
  border: 1px solid var(--admin-border);
}

/* Admin card extended styles */

.admin-card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: var(--admin-bg-light);
  border-bottom: 1px solid var(--admin-border);
}

.admin-card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

.admin-card-footer {
  padding: 0.75rem 1.25rem;
  background-color: var(--admin-bg-light);
  border-top: 1px solid var(--admin-border);
}

/* Admin alert styles */
.admin-alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.admin-alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.admin-alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.admin-alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.admin-alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

/* Admin badge styles */
.admin-badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.admin-badge-primary {
  color: white;
  background-color: var(--admin-primary);
}

.admin-badge-secondary {
  color: white;
  background-color: var(--admin-secondary);
}

.admin-badge-success {
  color: white;
  background-color: var(--admin-success);
}

.admin-badge-danger {
  color: white;
  background-color: var(--admin-error);
}

.admin-badge-warning {
  color: #212529;
  background-color: var(--admin-warning);
}

.admin-badge-info {
  color: white;
  background-color: var(--admin-info);
}
