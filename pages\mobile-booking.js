import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { toast } from 'react-toastify';
import { validateQRCode } from '@/lib/qr-code-manager';
import { supabase } from '@/lib/supabase';
import styles from '@/styles/mobile/MobileBooking.module.css';

/**
 * Mobile Booking Page
 * Handles the complete mobile booking flow from QR code scans
 */
export default function MobileBooking({ qrData, services, validationError }) {
  const router = useRouter();
  const { qr } = router.query;
  
  const [step, setStep] = useState(1);
  const [selectedService, setSelectedService] = useState(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [customerData, setCustomerData] = useState({
    name: '',
    email: '',
    phone: '',
    notes: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [availableSlots, setAvailableSlots] = useState([]);

  // Handle QR validation error
  if (validationError) {
    return (
      <div className={styles.container}>
        <Head>
          <title>Booking Error - Ocean Soul Sparkles</title>
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        </Head>
        
        <div className={styles.errorContainer}>
          <div className={styles.errorIcon}>⚠️</div>
          <h1 className={styles.errorTitle}>Booking Unavailable</h1>
          <p className={styles.errorMessage}>{validationError.error}</p>
          
          <div className={styles.errorActions}>
            <button 
              className={styles.primaryButton}
              onClick={() => router.push('/book-online')}
            >
              Book Online Instead
            </button>
            <button 
              className={styles.secondaryButton}
              onClick={() => router.push('/contact')}
            >
              Contact Us
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Step 1: Service Selection
  const renderServiceSelection = () => (
    <div className={styles.stepContainer}>
      <div className={styles.stepHeader}>
        <h2 className={styles.stepTitle}>Choose Your Service</h2>
        <p className={styles.stepSubtitle}>Select from available services for {qrData?.eventName}</p>
      </div>

      <div className={styles.servicesList}>
        {services.map(service => (
          <div 
            key={service.id}
            className={`${styles.serviceCard} ${selectedService?.id === service.id ? styles.selected : ''}`}
            onClick={() => setSelectedService(service)}
          >
            <div className={styles.serviceIcon}>{service.icon}</div>
            <div className={styles.serviceInfo}>
              <h3 className={styles.serviceName}>{service.title}</h3>
              <p className={styles.serviceDescription}>{service.description}</p>
              <div className={styles.servicePricing}>
                <span className={styles.servicePrice}>${service.price}</span>
                <span className={styles.serviceDuration}>{service.duration} min</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.stepActions}>
        <button 
          className={styles.primaryButton}
          disabled={!selectedService}
          onClick={() => {
            if (selectedService) {
              fetchAvailableSlots();
              setStep(2);
            }
          }}
        >
          Continue to Time Selection
        </button>
        <button 
          className={styles.secondaryButton}
          onClick={() => router.back()}
        >
          Back to Event Info
        </button>
      </div>
    </div>
  );

  // Fetch available time slots
  const fetchAvailableSlots = async () => {
    if (!selectedService) return;
    
    setIsLoading(true);
    try {
      // Generate time slots for the event duration
      const eventStart = new Date(qrData.eventStartDate);
      const eventEnd = new Date(qrData.eventEndDate);
      const serviceDuration = selectedService.duration;
      
      const slots = [];
      const current = new Date(eventStart);
      
      // Generate 30-minute intervals during event hours
      while (current < eventEnd) {
        const slotEnd = new Date(current.getTime() + (serviceDuration * 60000));
        if (slotEnd <= eventEnd) {
          slots.push({
            start: new Date(current),
            end: slotEnd,
            available: true // TODO: Check against existing bookings
          });
        }
        current.setMinutes(current.getMinutes() + 30);
      }
      
      setAvailableSlots(slots);
    } catch (error) {
      console.error('Error fetching time slots:', error);
      toast.error('Failed to load available times');
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Time Selection
  const renderTimeSelection = () => (
    <div className={styles.stepContainer}>
      <div className={styles.stepHeader}>
        <h2 className={styles.stepTitle}>Select Time</h2>
        <p className={styles.stepSubtitle}>
          {selectedService?.title} - {selectedService?.duration} minutes
        </p>
      </div>

      {isLoading ? (
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading available times...</p>
        </div>
      ) : (
        <div className={styles.timeSlots}>
          {availableSlots.map((slot, index) => (
            <button
              key={index}
              className={`${styles.timeSlot} ${selectedTimeSlot === slot ? styles.selected : ''} ${!slot.available ? styles.unavailable : ''}`}
              disabled={!slot.available}
              onClick={() => setSelectedTimeSlot(slot)}
            >
              <div className={styles.slotTime}>
                {slot.start.toLocaleTimeString('en-AU', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </div>
              <div className={styles.slotDate}>
                {slot.start.toLocaleDateString('en-AU', { 
                  weekday: 'short',
                  month: 'short', 
                  day: 'numeric' 
                })}
              </div>
            </button>
          ))}
        </div>
      )}

      <div className={styles.stepActions}>
        <button 
          className={styles.primaryButton}
          disabled={!selectedTimeSlot}
          onClick={() => selectedTimeSlot && setStep(3)}
        >
          Continue to Details
        </button>
        <button 
          className={styles.secondaryButton}
          onClick={() => setStep(1)}
        >
          Back to Services
        </button>
      </div>
    </div>
  );

  // Step 3: Customer Information
  const renderCustomerForm = () => (
    <div className={styles.stepContainer}>
      <div className={styles.stepHeader}>
        <h2 className={styles.stepTitle}>Your Details</h2>
        <p className={styles.stepSubtitle}>We need some information to confirm your booking</p>
      </div>

      <form className={styles.customerForm}>
        <div className={styles.formGroup}>
          <label className={styles.label}>Full Name *</label>
          <input
            type="text"
            className={styles.input}
            value={customerData.name}
            onChange={(e) => setCustomerData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter your full name"
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Email Address *</label>
          <input
            type="email"
            className={styles.input}
            value={customerData.email}
            onChange={(e) => setCustomerData(prev => ({ ...prev, email: e.target.value }))}
            placeholder="Enter your email"
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Phone Number *</label>
          <input
            type="tel"
            className={styles.input}
            value={customerData.phone}
            onChange={(e) => setCustomerData(prev => ({ ...prev, phone: e.target.value }))}
            placeholder="Enter your phone number"
            required
          />
        </div>

        <div className={styles.formGroup}>
          <label className={styles.label}>Special Requests (Optional)</label>
          <textarea
            className={styles.textarea}
            value={customerData.notes}
            onChange={(e) => setCustomerData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Any special requests or notes..."
            rows={3}
          />
        </div>
      </form>

      <div className={styles.stepActions}>
        <button 
          className={styles.primaryButton}
          disabled={!customerData.name || !customerData.email || !customerData.phone}
          onClick={() => setStep(4)}
        >
          Continue to Payment
        </button>
        <button 
          className={styles.secondaryButton}
          onClick={() => setStep(2)}
        >
          Back to Time Selection
        </button>
      </div>
    </div>
  );

  // Step 4: Booking Summary & Payment
  const renderBookingSummary = () => (
    <div className={styles.stepContainer}>
      <div className={styles.stepHeader}>
        <h2 className={styles.stepTitle}>Confirm Booking</h2>
        <p className={styles.stepSubtitle}>Review your booking details</p>
      </div>

      <div className={styles.bookingSummary}>
        <div className={styles.summarySection}>
          <h3 className={styles.summaryTitle}>Event</h3>
          <p className={styles.summaryText}>{qrData?.eventName}</p>
          <p className={styles.summaryText}>{qrData?.eventLocation}</p>
        </div>

        <div className={styles.summarySection}>
          <h3 className={styles.summaryTitle}>Service</h3>
          <p className={styles.summaryText}>{selectedService?.title}</p>
          <p className={styles.summaryText}>{selectedService?.duration} minutes</p>
        </div>

        <div className={styles.summarySection}>
          <h3 className={styles.summaryTitle}>Time</h3>
          <p className={styles.summaryText}>
            {selectedTimeSlot?.start.toLocaleDateString('en-AU', { 
              weekday: 'long',
              year: 'numeric',
              month: 'long', 
              day: 'numeric' 
            })}
          </p>
          <p className={styles.summaryText}>
            {selectedTimeSlot?.start.toLocaleTimeString('en-AU', { 
              hour: '2-digit', 
              minute: '2-digit' 
            })} - {selectedTimeSlot?.end.toLocaleTimeString('en-AU', { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </p>
        </div>

        <div className={styles.summarySection}>
          <h3 className={styles.summaryTitle}>Customer</h3>
          <p className={styles.summaryText}>{customerData.name}</p>
          <p className={styles.summaryText}>{customerData.email}</p>
          <p className={styles.summaryText}>{customerData.phone}</p>
        </div>

        <div className={styles.totalSection}>
          <div className={styles.totalRow}>
            <span className={styles.totalLabel}>Total:</span>
            <span className={styles.totalAmount}>${selectedService?.price}</span>
          </div>
        </div>
      </div>

      <div className={styles.stepActions}>
        <button 
          className={styles.primaryButton}
          onClick={handleBookingSubmit}
          disabled={isLoading}
        >
          {isLoading ? 'Processing...' : 'Confirm & Pay'}
        </button>
        <button 
          className={styles.secondaryButton}
          onClick={() => setStep(3)}
          disabled={isLoading}
        >
          Back to Details
        </button>
      </div>
    </div>
  );

  // Handle booking submission
  const handleBookingSubmit = async () => {
    setIsLoading(true);
    try {
      const bookingData = {
        qrCode: qr,
        service: selectedService,
        timeSlot: selectedTimeSlot,
        customer: customerData,
        eventData: qrData
      };

      // TODO: Implement booking creation and payment processing
      console.log('Submitting booking:', bookingData);
      
      // For now, show success message
      toast.success('Booking submitted successfully!');
      router.push('/booking-confirmation');
      
    } catch (error) {
      console.error('Error submitting booking:', error);
      toast.error('Failed to submit booking. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <Head>
        <title>Book Appointment - {qrData?.eventName} - Ocean Soul Sparkles</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className={styles.header}>
        <div className={styles.logo}>
          <img src="/images/logo.png" alt="Ocean Soul Sparkles" />
        </div>
        <div className={styles.stepIndicator}>
          Step {step} of 4
        </div>
      </div>

      <div className={styles.content}>
        {step === 1 && renderServiceSelection()}
        {step === 2 && renderTimeSelection()}
        {step === 3 && renderCustomerForm()}
        {step === 4 && renderBookingSummary()}
      </div>
    </div>
  );
}

/**
 * Server-side props to validate QR code and fetch event data
 */
export async function getServerSideProps({ query }) {
  const { qr } = query;

  if (!qr) {
    return {
      redirect: {
        destination: '/book-online',
        permanent: false,
      },
    };
  }

  try {
    // Validate QR code
    const validationResult = await validateQRCode(qr);
    
    if (!validationResult.isValid) {
      return {
        props: {
          qrData: null,
          validationError: validationResult,
          services: []
        }
      };
    }

    const qrData = validationResult.qrData;

    // Fetch available services for this event
    let services = [];
    if (qrData.availableServices && qrData.availableServices.length > 0) {
      const { data: serviceData, error: serviceError } = await supabase
        .from('services')
        .select('*')
        .in('id', qrData.availableServices)
        .eq('status', 'active');

      if (!serviceError && serviceData) {
        services = serviceData.map(service => ({
          id: service.id,
          title: service.name,
          description: service.description,
          price: service.price,
          duration: service.duration,
          category: service.category,
          icon: getCategoryIcon(service.category)
        }));
      }
    } else {
      // If no specific services assigned, get all active services
      const { data: allServices, error: allServicesError } = await supabase
        .from('services')
        .select('*')
        .eq('status', 'active')
        .limit(6);

      if (!allServicesError && allServices) {
        services = allServices.map(service => ({
          id: service.id,
          title: service.name,
          description: service.description,
          price: service.price,
          duration: service.duration,
          category: service.category,
          icon: getCategoryIcon(service.category)
        }));
      }
    }

    return {
      props: {
        qrData,
        validationError: null,
        services
      }
    };

  } catch (error) {
    console.error('Error in mobile booking page:', error);
    return {
      props: {
        qrData: null,
        validationError: {
          error: 'Failed to load booking data',
          code: 'SYSTEM_ERROR'
        },
        services: []
      }
    };
  }
}

/**
 * Get category icon for service
 */
function getCategoryIcon(category) {
  const iconMap = {
    'painting': '🎨',
    'airbrush': '🎨',
    'braiding': '💇',
    'hair': '💇',
    'glitter': '✨',
    'sparkle': '✨',
    'special': '🎭',
    'uv': '🌟'
  };
  return iconMap[category] || '🎨';
}
