import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for managing individual event expenses
 * Handles GET, PUT, and DELETE operations for specific expenses
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  const { eventId, expenseId } = req.query;
  
  console.log(`[${requestId}] Individual Expense API called: ${req.method} for expense ${expenseId} in event ${eventId}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetExpense(req, res, eventId, expenseId, requestId);
    } else if (req.method === 'PUT') {
      return await handleUpdateExpense(req, res, eventId, expenseId, user, requestId);
    } else if (req.method === 'DELETE') {
      return await handleDeleteExpense(req, res, eventId, expenseId, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch single expense
 */
async function handleGetExpense(req, res, eventId, expenseId, requestId) {
  try {
    console.log(`[${requestId}] Fetching expense:`, expenseId);

    const { data: expense, error } = await supabase
      .from('event_expenses')
      .select(`
        id,
        expense_name,
        amount,
        description,
        vendor_name,
        expense_date,
        payment_method,
        is_reimbursable,
        reimbursed,
        reimbursed_date,
        created_at,
        updated_at,
        category_id,
        expense_categories!inner(
          id,
          name,
          icon,
          color
        )
      `)
      .eq('id', expenseId)
      .eq('event_id', eventId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        console.log(`[${requestId}] Expense not found:`, expenseId);
        return res.status(404).json({ error: 'Expense not found' });
      }
      console.error(`[${requestId}] Error fetching expense:`, error);
      throw error;
    }

    // Format response with category information
    const formattedExpense = {
      ...expense,
      category_name: expense.expense_categories.name,
      category_icon: expense.expense_categories.icon,
      category_color: expense.expense_categories.color
    };

    console.log(`[${requestId}] Expense fetched successfully`);
    return res.status(200).json({ expense: formattedExpense });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetExpense:`, error);
    throw error;
  }
}

/**
 * Handle PUT request - update expense
 */
async function handleUpdateExpense(req, res, eventId, expenseId, user, requestId) {
  try {
    const {
      category_id,
      expense_name,
      amount,
      description,
      vendor_name,
      expense_date,
      payment_method,
      is_reimbursable,
      reimbursed
    } = req.body;

    console.log(`[${requestId}] Updating expense:`, { expenseId, expense_name, amount });

    // Validate required fields
    if (!category_id || !expense_name || !amount || !expense_date) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['category_id', 'expense_name', 'amount', 'expense_date']
      });
    }

    // Validate amount
    const expenseAmount = parseFloat(amount);
    if (isNaN(expenseAmount) || expenseAmount < 0) {
      return res.status(400).json({ 
        error: 'Amount must be a valid positive number' 
      });
    }

    // Check if expense exists and belongs to the event
    const { data: existingExpense, error: fetchError } = await supabase
      .from('event_expenses')
      .select('id, event_id, created_by')
      .eq('id', expenseId)
      .eq('event_id', eventId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Expense not found' });
      }
      throw fetchError;
    }

    // Verify category exists
    const { data: category, error: categoryError } = await supabase
      .from('expense_categories')
      .select('id, name')
      .eq('id', category_id)
      .single();

    if (categoryError) {
      if (categoryError.code === 'PGRST116') {
        return res.status(400).json({ error: 'Invalid expense category' });
      }
      throw categoryError;
    }

    // Prepare update data
    const updateData = {
      category_id,
      expense_name,
      amount: expenseAmount,
      description: description || '',
      vendor_name: vendor_name || '',
      expense_date,
      payment_method: payment_method || 'card',
      is_reimbursable: is_reimbursable || false,
      updated_at: new Date().toISOString()
    };

    // Handle reimbursement status
    if (typeof reimbursed === 'boolean') {
      updateData.reimbursed = reimbursed;
      if (reimbursed && !existingExpense.reimbursed) {
        updateData.reimbursed_date = new Date().toISOString().split('T')[0];
      } else if (!reimbursed) {
        updateData.reimbursed_date = null;
      }
    }

    // Update expense
    const { data: expense, error: updateError } = await supabase
      .from('event_expenses')
      .update(updateData)
      .eq('id', expenseId)
      .eq('event_id', eventId)
      .select(`
        id,
        expense_name,
        amount,
        description,
        vendor_name,
        expense_date,
        payment_method,
        is_reimbursable,
        reimbursed,
        reimbursed_date,
        created_at,
        updated_at,
        category_id,
        expense_categories!inner(
          id,
          name,
          icon,
          color
        )
      `)
      .single();

    if (updateError) {
      console.error(`[${requestId}] Error updating expense:`, updateError);
      throw updateError;
    }

    // Format response with category information
    const formattedExpense = {
      ...expense,
      category_name: expense.expense_categories.name,
      category_icon: expense.expense_categories.icon,
      category_color: expense.expense_categories.color
    };

    console.log(`[${requestId}] Expense updated successfully:`, expense.id);
    return res.status(200).json({ 
      expense: formattedExpense,
      message: 'Expense updated successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleUpdateExpense:`, error);
    throw error;
  }
}

/**
 * Handle DELETE request - delete expense
 */
async function handleDeleteExpense(req, res, eventId, expenseId, user, requestId) {
  try {
    console.log(`[${requestId}] Deleting expense:`, expenseId);

    // Check if expense exists and belongs to the event
    const { data: existingExpense, error: fetchError } = await supabase
      .from('event_expenses')
      .select('id, event_id, expense_name, amount')
      .eq('id', expenseId)
      .eq('event_id', eventId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Expense not found' });
      }
      throw fetchError;
    }

    // Delete the expense
    const { error: deleteError } = await supabase
      .from('event_expenses')
      .delete()
      .eq('id', expenseId)
      .eq('event_id', eventId);

    if (deleteError) {
      console.error(`[${requestId}] Error deleting expense:`, deleteError);
      throw deleteError;
    }

    console.log(`[${requestId}] Expense deleted successfully:`, expenseId);
    return res.status(200).json({ 
      message: 'Expense deleted successfully',
      expenseId: expenseId,
      deletedExpense: {
        name: existingExpense.expense_name,
        amount: existingExpense.amount
      }
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleDeleteExpense:`, error);
    throw error;
  }
}
