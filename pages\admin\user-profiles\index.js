import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import UserProfilesOverview from '@/components/admin/user-profiles/UserProfilesOverview'
import PermissionMatrix from '@/components/admin/user-profiles/PermissionMatrix'
import CommissionManagement from '@/components/admin/user-profiles/CommissionManagement'
import ProfileSettings from '@/components/admin/user-profiles/ProfileSettings'
import { useAuth } from '@/contexts/AuthContext'
import styles from '@/styles/admin/UserProfilesPage.module.css'

export default function UserProfilesPage() {
  const router = useRouter()
  const { isDev } = useAuth()
  const [activeTab, setActiveTab] = useState('overview')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Set initial tab from URL parameter
  useEffect(() => {
    if (router.query.tab) {
      setActiveTab(router.query.tab)
    }
  }, [router.query.tab])

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: '📊',
      description: 'User profiles and role distribution'
    },
    {
      id: 'permissions',
      label: 'Permissions',
      icon: '🔐',
      description: 'Role-based access control matrix'
    },
    {
      id: 'commissions',
      label: 'Commissions',
      icon: '💰',
      description: 'Artist and braider commission rates'
    },
    {
      id: 'settings',
      label: 'Profile Settings',
      icon: '⚙️',
      description: 'User profile customization and visibility'
    }
  ]

  // Handle tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    setError(null)
    // Update URL without page reload
    router.push(`/admin/user-profiles?tab=${tabId}`, undefined, { shallow: true })
  }

  return (
    <ProtectedRoute devOnly>
      <AdminLayout title="User Profiles Management">
        <div className={styles.userProfilesPage}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h1>User Profiles Management</h1>
              <p>Comprehensive user profile and permission management system</p>
            </div>
            <div className={styles.headerActions}>
              <div className={styles.roleIndicator}>
                <span className={styles.devBadge}>DEV ACCESS</span>
              </div>
            </div>
          </div>

          {error && (
            <div className={styles.errorAlert}>
              <div className={styles.errorIcon}>⚠️</div>
              <div className={styles.errorContent}>
                <h3>Error</h3>
                <p>{error}</p>
              </div>
              <button
                className={styles.errorClose}
                onClick={() => setError(null)}
              >
                ✕
              </button>
            </div>
          )}

          {/* Tab Navigation */}
          <div className={styles.tabNavigation}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`${styles.tab} ${activeTab === tab.id ? styles.activeTab : ''}`}
                onClick={() => handleTabChange(tab.id)}
                disabled={loading}
              >
                <div className={styles.tabHeader}>
                  <span className={styles.tabIcon}>{tab.icon}</span>
                  <span className={styles.tabLabel}>{tab.label}</span>
                </div>
                <div className={styles.tabDescription}>
                  {tab.description}
                </div>
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className={styles.tabContent}>
            {loading && (
              <div className={styles.loadingOverlay}>
                <div className={styles.loadingSpinner}></div>
                <p>Loading...</p>
              </div>
            )}

            {activeTab === 'overview' && (
              <UserProfilesOverview
                onError={setError}
                onLoading={setLoading}
              />
            )}

            {activeTab === 'permissions' && (
              <PermissionMatrix
                onError={setError}
                onLoading={setLoading}
              />
            )}

            {activeTab === 'commissions' && (
              <CommissionManagement
                onError={setError}
                onLoading={setLoading}
              />
            )}

            {activeTab === 'settings' && (
              <ProfileSettings
                onError={setError}
                onLoading={setLoading}
              />
            )}
          </div>

          {/* Help Section */}
          <div className={styles.helpSection}>
            <div className={styles.helpCard}>
              <h3>💡 User Profile Management Guide</h3>
              <div className={styles.helpContent}>
                <div className={styles.helpItem}>
                  <strong>Overview:</strong> View user distribution, role statistics, and profile summaries
                </div>
                <div className={styles.helpItem}>
                  <strong>Permissions:</strong> Configure role-based access control for admin panel features
                </div>
                <div className={styles.helpItem}>
                  <strong>Commissions:</strong> Set and manage commission rates for artists and braiders
                </div>
                <div className={styles.helpItem}>
                  <strong>Profile Settings:</strong> Customize user profile visibility and dashboard widgets
                </div>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
