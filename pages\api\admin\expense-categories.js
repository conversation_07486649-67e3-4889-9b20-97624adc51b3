import { authenticateAdminRequest } from '@/lib/admin-auth';
import { supabase } from '@/lib/supabase';

/**
 * API endpoint for managing expense categories
 * Handles GET (list categories) and POST (create category) operations
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8);
  
  console.log(`[${requestId}] Expense Categories API called: ${req.method}`);

  try {
    // Authenticate admin request
    const authResult = await authenticateAdminRequest(req);
    if (!authResult.authorized) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, role } = authResult;
    console.log(`[${requestId}] Authenticated user: ${user.email} (${role})`);

    if (req.method === 'GET') {
      return await handleGetCategories(req, res, requestId);
    } else if (req.method === 'POST') {
      return await handleCreateCategory(req, res, user, requestId);
    } else {
      return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error(`[${requestId}] Unexpected error:`, error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request - fetch expense categories
 */
async function handleGetCategories(req, res, requestId) {
  try {
    console.log(`[${requestId}] Fetching expense categories`);

    const { data: categories, error } = await supabase
      .from('expense_categories')
      .select(`
        id,
        name,
        description,
        is_predefined,
        icon,
        color,
        created_at,
        updated_at
      `)
      .order('is_predefined', { ascending: false })
      .order('name', { ascending: true });

    if (error) {
      console.error(`[${requestId}] Error fetching categories:`, error);
      throw error;
    }

    console.log(`[${requestId}] Found ${categories.length} categories`);
    return res.status(200).json({ 
      categories: categories || [],
      count: categories ? categories.length : 0
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleGetCategories:`, error);
    throw error;
  }
}

/**
 * Handle POST request - create new category
 */
async function handleCreateCategory(req, res, user, requestId) {
  try {
    const {
      name,
      description,
      icon,
      color
    } = req.body;

    console.log(`[${requestId}] Creating expense category:`, { name, icon });

    // Validate required fields
    if (!name) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        required: ['name']
      });
    }

    // Check if category name already exists
    const { data: existingCategory, error: checkError } = await supabase
      .from('expense_categories')
      .select('id, name')
      .eq('name', name.trim())
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingCategory) {
      return res.status(400).json({ 
        error: 'Category name already exists',
        existingCategory: existingCategory.name
      });
    }

    // Create category
    const { data: category, error: createError } = await supabase
      .from('expense_categories')
      .insert([{
        name: name.trim(),
        description: description?.trim() || '',
        icon: icon || '📦',
        color: color || '#6b7280',
        is_predefined: false
      }])
      .select()
      .single();

    if (createError) {
      console.error(`[${requestId}] Error creating category:`, createError);
      throw createError;
    }

    console.log(`[${requestId}] Category created successfully:`, category.id);
    return res.status(201).json({ 
      category: category,
      message: 'Category created successfully'
    });

  } catch (error) {
    console.error(`[${requestId}] Error in handleCreateCategory:`, error);
    throw error;
  }
}
